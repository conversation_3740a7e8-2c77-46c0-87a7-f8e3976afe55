package com.snct.service.device;

import com.snct.dctcore.commoncore.domain.hbase.AttitudeHbaseVo;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;

import com.snct.system.service.DeviceDataQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: AttitudeService
 * @Description: 姿态数据服务类
 * @author: wzewei
 * @date: 2025-07-30 14:53
 */
@Service
@Transactional(readOnly = true)
public class AttitudeService {

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;
    @Autowired
    private DeviceDataQueryService deviceDataQueryService;

    private final Logger logger = LoggerFactory.getLogger(AttitudeService.class);
    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<AttitudeHbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.ATTITUDE.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new AttitudeHbaseVo(), tableName, rowList);
    }

    public AttitudeHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.ATTITUDE.getAlias(), deviceCode,100);
        return hBaseDaoUtil.getLatestRow(new AttitudeHbaseVo(), tableName);
    }

    /**
     * 从MySQL数据库按时间范围查询Attitude数据
     * 注意：MySQL存储的是北斗数据，本身就有时间间隔，不需要抽稀处理
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型字符串（如"attitude"）
     * @param interval 时间间隔（保留参数，北斗数据不需要抽稀，直接按时间范围查询）
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return Attitude数据列表
     */
    public List<AttitudeHbaseVo> queryByTimeFromMysql(String sn, String deviceCode, String deviceType,
                                                      Integer interval, Long startTime, Long endTime) {
        try {
            logger.debug("从MySQL查询Attitude数据: sn={}, deviceCode={}, deviceType={}, startTime={}, endTime={}",
                        sn, deviceCode, deviceType, startTime, endTime);

            // 北斗数据本身就有时间间隔，直接按时间范围查询所有数据
            List<Map<String, Object>> dataList = deviceDataQueryService.queryDeviceDataByTimeRange(sn, deviceCode, deviceType, startTime, endTime);

            if (dataList == null || dataList.isEmpty()) {
                logger.warn("MySQL查询Attitude数据为空: sn={}, deviceCode={}", sn, deviceCode);
                return new ArrayList<>();
            }

            // 将MySQL查询结果转换为AttitudeHbaseVo对象
            List<AttitudeHbaseVo> attitudeDataList = dataList.stream()
                .map(this::convertMysqlDataToAttitudeHbaseVo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            logger.debug("MySQL查询Attitude数据成功，返回{}条记录", attitudeDataList.size());
            return attitudeDataList;

        } catch (Exception e) {
            logger.error("从MySQL查询Attitude数据失败: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 从MySQL数据库获取Attitude设备的最新一条数据
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型字符串（如"attitude"）
     * @return Attitude最新数据
     */
    public AttitudeHbaseVo getLatestDataFromMysql(String sn, String deviceCode, String deviceType) {
        try {
            logger.debug("从MySQL获取Attitude最新数据: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType);

            Map<String, Object> latestData = deviceDataQueryService.getLatestDeviceDataCompatible(sn, deviceCode, deviceType);

            if (latestData == null) {
                logger.warn("MySQL中未找到Attitude最新数据: sn={}, deviceCode={}", sn, deviceCode);
                return null;
            }

            AttitudeHbaseVo attitudeData = convertMysqlDataToAttitudeHbaseVo(latestData);

            if (attitudeData != null) {
                logger.debug("MySQL获取Attitude最新数据成功: sn={}, deviceCode={}, time={}",
                           sn, deviceCode, attitudeData.getInitialBjTime());
            }

            return attitudeData;

        } catch (Exception e) {
            logger.error("从MySQL获取Attitude最新数据失败: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType, e);
            return null;
        }
    }

    /**
     * 将MySQL查询结果转换为AttitudeHbaseVo对象
     *
     * @param mysqlData MySQL查询结果
     * @return AttitudeHbaseVo对象
     */
    private AttitudeHbaseVo convertMysqlDataToAttitudeHbaseVo(Map<String, Object> mysqlData) {
        if (mysqlData == null) {
            return null;
        }

        try {
            AttitudeHbaseVo attitudeVo = new AttitudeHbaseVo();

            // 设置基础字段
            attitudeVo.setId(getString(mysqlData, "id"));
            attitudeVo.setInitialTime(getString(mysqlData, "initialTime"));
            attitudeVo.setInitialBjTime(getString(mysqlData, "initialBjTime"));
            attitudeVo.setUtcTime(getString(mysqlData, "utcTime"));

            // 设置Attitude特有字段
            attitudeVo.setLat(getString(mysqlData, "lat"));
            attitudeVo.setLon(getString(mysqlData, "lon"));
            attitudeVo.setRolling(getString(mysqlData, "rolling"));
            attitudeVo.setPitch(getString(mysqlData, "pitch"));
            attitudeVo.setHeight(getString(mysqlData, "height"));
            attitudeVo.setHeading(getString(mysqlData, "heading"));
            attitudeVo.setSpeed(getString(mysqlData, "speed"));
            attitudeVo.setDistance(getString(mysqlData, "distance"));
            attitudeVo.setStationName(getString(mysqlData, "stationName"));

            return attitudeVo;

        } catch (Exception e) {
            logger.error("转换MySQL数据到AttitudeHbaseVo失败", e);
            return null;
        }
    }

    /**
     * 安全获取字符串值
     *
     * @param map 数据映射
     * @param key 键名
     * @return 字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

}
