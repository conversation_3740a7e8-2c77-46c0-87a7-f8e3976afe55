<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuCruiseMapper">
    
    <resultMap type="BuCruise" id="BuCruiseResult">
        <result property="cruiseId"    column="cruise_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"    column="dept_name"    />
        <result property="sn"    column="sn"    />
        <result property="shipName"    column="name"    />
        <result property="captain"    column="captain"    />
        <result property="code"    column="code"    />
        <result property="startTime"    column="start_time"    />
        <result property="finishTime"    column="finish_time"    />
        <result property="totalDays"    column="total_days"    />
        <result property="historyMileage"    column="history_mileage"    />
        <result property="startPort"    column="start_port"    />
        <result property="endPort"    column="end_port"    />
        <result property="seaArea"    column="sea_area"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="moduleJson"    column="module_json"    />
    </resultMap>

    <sql id="selectBuCruiseVo">
        select c.cruise_id, c.dept_id, c.sn, c.captain, c.code, c.start_time, c.finish_time, c.total_days, c.history_mileage,
               c.start_port, c.end_port, c.sea_area, c.create_by,
               c.create_time, c.update_by, c.update_time, c.module_json ,
               d.dept_name,s.name
        from bu_cruise c
        INNER join sys_dept d on d.dept_id = c.dept_id
        INNER join bu_ship s on s.sn = c.sn
    </sql>

    <select id="selectNewCruise" parameterType="BuCruise" resultMap="BuCruiseResult">
        <include refid="selectBuCruiseVo"/>
        <where>  
            <if test="deptId != null "> and c.dept_id = #{deptId}</if>
            <if test="sn != null  and sn != ''"> and c.sn = #{sn}</if>
        </where>
        ${params.dataScope}
        order by startTime desc limit 1
    </select>


    <select id="selectBuCruiseList" parameterType="BuCruise" resultMap="BuCruiseResult">
        <include refid="selectBuCruiseVo"/>
        <where>
            <if test="deptId != null "> and c.dept_id = #{deptId}</if>
            <if test="sn != null  and sn != ''"> and c.sn = #{sn}</if>
            <if test="captain != null  and captain != ''"> and c.captain like concat('%', #{captain}, '%')</if>
            <if test="code != null  and code != ''"> and c.code like concat('%', #{code}, '%')</if>
            <if test="deptName != null  and deptName != ''"> and d.dept_name like concat('%', #{deptName}, '%')</if>
            <if test="shipName != null  and shipName != ''"> and s.name like concat('%', #{shipName}, '%')</if>
            <if test="startTime != null "> and c.start_time = #{startTime}</if>
            <if test="finishTime != null "> and c.finish_time = #{finishTime}</if>
            <if test="totalDays != null "> and c.total_days = #{totalDays}</if>
            <if test="historyMileage != null "> and c.history_mileage = #{historyMileage}</if>
            <if test="startPort != null  and startPort != ''"> and c.start_port = #{startPort}</if>
            <if test="endPort != null  and endPort != ''"> and c.end_port = #{endPort}</if>
            <if test="seaArea != null  and seaArea != ''"> and c.sea_area = #{seaArea}</if>
            <if test="moduleJson != null  and moduleJson != ''"> and c.module_json = #{moduleJson}</if>
        </where>
        ${params.dataScope}
        order by c.start_time desc
    </select>

    
    <select id="selectBuCruiseByCruiseId" parameterType="Long" resultMap="BuCruiseResult">
        <include refid="selectBuCruiseVo"/>
        where c.cruise_id = #{cruiseId}
    </select>

    <insert id="insertBuCruise" parameterType="BuCruise" useGeneratedKeys="true" keyProperty="cruiseId">
        insert into bu_cruise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="sn != null and sn != ''">sn,</if>
            <if test="captain != null">captain,</if>
            <if test="code != null and code != ''">code,</if>
            <if test="startTime != null">start_time,</if>
            <if test="finishTime != null">finish_time,</if>
            <if test="totalDays != null">total_days,</if>
            <if test="historyMileage != null">history_mileage,</if>
            <if test="startPort != null">start_port,</if>
            <if test="endPort != null">end_port,</if>
            <if test="seaArea != null">sea_area,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="moduleJson != null">module_json,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="sn != null and sn != ''">#{sn},</if>
            <if test="captain != null">#{captain},</if>
            <if test="code != null and code != ''">#{code},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="finishTime != null">#{finishTime},</if>
            <if test="totalDays != null">#{totalDays},</if>
            <if test="historyMileage != null">#{historyMileage},</if>
            <if test="startPort != null">#{startPort},</if>
            <if test="endPort != null">#{endPort},</if>
            <if test="seaArea != null">#{seaArea},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="moduleJson != null">#{moduleJson},</if>
         </trim>
    </insert>

    <update id="updateBuCruise" parameterType="BuCruise">
        update bu_cruise
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="sn != null and sn != ''">sn = #{sn},</if>
            <if test="captain != null">captain = #{captain},</if>
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="finishTime != null">finish_time = #{finishTime},</if>
            <if test="totalDays != null">total_days = #{totalDays},</if>
            <if test="historyMileage != null">history_mileage = #{historyMileage},</if>
            <if test="startPort != null">start_port = #{startPort},</if>
            <if test="endPort != null">end_port = #{endPort},</if>
            <if test="seaArea != null">sea_area = #{seaArea},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="moduleJson != null">module_json = #{moduleJson},</if>
        </trim>
        where cruise_id = #{cruiseId}
    </update>

    <delete id="deleteBuCruiseByCruiseId" parameterType="Long">
        delete from bu_cruise where cruise_id = #{cruiseId}
    </delete>

    <delete id="deleteBuCruiseByCruiseIds" parameterType="String">
        delete from bu_cruise where cruise_id in 
        <foreach item="cruiseId" collection="array" open="(" separator="," close=")">
            #{cruiseId}
        </foreach>
    </delete>
</mapper>