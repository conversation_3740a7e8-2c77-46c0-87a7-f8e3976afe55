package com.snct.system.mapper;

import java.util.List;
import com.snct.system.domain.data.BuDataAmplifier;

/**
 * 功放数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public interface BuDataAmplifierMapper 
{
    /**
     * 查询功放数据
     * 
     * @param id 功放数据主键
     * @return 功放数据
     */
    public BuDataAmplifier selectBuDataAmplifierById(Long id);

    /**
     * 查询功放数据列表
     * 
     * @param buDataAmplifier 功放数据
     * @return 功放数据集合
     */
    public List<BuDataAmplifier> selectBuDataAmplifierList(BuDataAmplifier buDataAmplifier);

    /**
     * 新增功放数据
     * 
     * @param buDataAmplifier 功放数据
     * @return 结果
     */
    public int insertBuDataAmplifier(BuDataAmplifier buDataAmplifier);

    /**
     * 修改功放数据
     * 
     * @param buDataAmplifier 功放数据
     * @return 结果
     */
    public int updateBuDataAmplifier(BuDataAmplifier buDataAmplifier);

    /**
     * 删除功放数据
     * 
     * @param id 功放数据主键
     * @return 结果
     */
    public int deleteBuDataAmplifierById(Long id);

    /**
     * 批量删除功放数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuDataAmplifierByIds(Long[] ids);
}
