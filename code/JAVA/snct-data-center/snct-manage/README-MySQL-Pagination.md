# MySQL 存储分页查询功能说明

## 概述

本次更新为设备数据查询服务添加了 MySQL 存储的分页查询功能，实现了与 HBase 查询接口的兼容性，支持统一的数据查询接口。

## 新增功能

### 1. MySQL 分页查询服务 (DeviceDataQueryService)

#### 新增方法：

- `queryDeviceDataPageCompatible()` - 兼容 HBase 格式的分页查询
- `queryDeviceDataWithInterval()` - 支持时间间隔抽稀的分页查询
- `countDeviceDataWithInterval()` - 支持时间间隔的数据统计
- `convertToHBaseCompatibleFormat()` - 数据格式转换
- `getLatestDeviceDataCompatible()` - 获取最新数据（HBase 兼容格式）

### 2. MySQL 专用控制器 (BuDeviceDataMysqlController)

提供专门的 MySQL 存储查询接口：

- `GET /business/data/mysql/list` - 分页查询设备数据
- `GET /business/data/mysql/list/interval` - 支持时间间隔抽稀的分页查询
- `GET /business/data/mysql/latest` - 获取设备最新数据
- `GET /business/data/mysql/count` - 统计设备数据总数
- `GET /business/data/mysql/count/interval` - 支持时间间隔的数据统计
- `GET /business/data/mysql/timerange` - 获取设备数据时间范围
- `GET /business/data/mysql/table/exists` - 检查设备表是否存在
- `GET /business/data/mysql/table/info` - 获取设备表信息
- `GET /business/data/mysql/table/list` - 获取所有设备数据表列表

### 3. 统一查询服务 (DeviceDataUnifiedService)

提供统一的查询接口，支持 HBase 和 MySQL 双存储：

- 自动存储类型选择
- 存储故障自动切换
- 性能对比功能
- 批量查询支持

### 4. 统一查询控制器 (BuDeviceDataUnifiedController)

提供统一的 REST API 接口：

- `GET /business/data/unified/list` - 统一分页查询接口
- `GET /business/data/unified/latest` - 统一最新数据查询
- `GET /business/data/unified/count` - 统一数据统计
- `GET /business/data/unified/storage/status` - 存储状态查询
- `GET /business/data/unified/performance/compare` - 性能对比
- `GET /business/data/unified/batch/latest` - 批量最新数据查询
- `GET /business/data/unified/statistics` - 数据统计信息

## 配置说明

### 存储类型配置

在 `application.yml` 或 `application-device-storage.yml` 中配置：

```yaml
device:
  data:
    storage:
      type: auto  # mysql | hbase | auto
```

- `mysql`: 仅使用 MySQL 存储
- `hbase`: 仅使用 HBase 存储
- `auto`: 自动选择（优先 HBase，不可用时使用 MySQL）

## API 使用示例

### 1. MySQL 分页查询

```bash
# 基本分页查询
GET /business/data/mysql/list?deviceId=1&pageNum=1&pageSize=10&sortOrder=desc

# 时间范围查询
GET /business/data/mysql/list?deviceId=1&startTime=1693737600000&endTime=1693824000000&pageNum=1&pageSize=10

# 支持时间间隔抽稀的查询
GET /business/data/mysql/list/interval?deviceId=1&interval=5&pageNum=1&pageSize=10
```

### 2. 统一接口查询

```bash
# 自动选择存储类型
GET /business/data/unified/list?deviceId=1&pageNum=1&pageSize=10

# 指定存储类型
GET /business/data/unified/list?deviceId=1&pageNum=1&pageSize=10&storageType=mysql

# 性能对比
GET /business/data/unified/performance/compare?deviceId=1&pageNum=1&pageSize=10
```

### 3. 数据统计

```bash
# MySQL 数据统计
GET /business/data/mysql/count?deviceId=1&startTime=1693737600000&endTime=1693824000000

# 统一接口数据统计
GET /business/data/unified/statistics?deviceId=1&startTime=1693737600000&endTime=1693824000000
```

## 数据格式说明

### 查询返回格式

```json
{
  "data": [
    {
      "id": "123456789",
      "initialTime": 1693737600000,
      "initialBjTime": "2023-09-03 16:00:00",
      "utcTime": "2023-09-03T08:00:00Z",
      // 设备特定字段...
    }
  ],
  "total": 1000
}
```

### 支持的设备类型

- GPS: 纬度、经度、半球信息
- AWS: 气象数据（风向、风速、温度、湿度、气压等）
- MODEM: 卫星猫数据（信号强度、速率、功率等）
- PDU: 电源分配单元数据（电压、电流、功率、8通道状态）
- ATTITUDE: 姿态仪数据（横摇、纵摇、航向、高度等）
- AMPLIFIER: 功放数据（衰减、温度、输出功率、状态）

## 时间间隔抽稀功能

支持按时间间隔对数据进行抽稀，减少返回的数据量：

- `interval=5`: 每5分钟取一条数据
- `interval=15`: 每15分钟取一条数据
- `interval=60`: 每小时取一条数据

抽稀算法：在每个时间窗口内选择时间戳最新的一条数据。

## 性能优化

### 1. 索引优化

确保 MySQL 表在 `initial_time` 字段上有索引：

```sql
CREATE INDEX idx_initial_time ON device_table_name (initial_time);
```

### 2. 分页优化

- 使用 LIMIT/OFFSET 进行分页
- 支持按时间倒序排列，优先显示最新数据
- 大数据量查询时建议使用时间间隔抽稀

### 3. 查询缓存

可在配置中启用查询缓存以提高性能：

```yaml
device:
  data:
    storage:
      performance:
        cache-enabled: true
        cache-ttl: 300
```

## 注意事项

1. **数据一致性**: MySQL 和 HBase 的数据格式已统一，但字段命名从下划线转换为驼峰命名
2. **时间格式**: 统一使用时间戳（毫秒）进行时间查询
3. **分页限制**: 建议单次查询不超过 1000 条记录
4. **存储选择**: 在自动模式下，系统会根据可用性自动选择存储类型
5. **设备类型转换**: Device.getType() 返回 Integer 类型，需要通过 DeviceTypeEnum.getByValue().getAlias() 转换为字符串类型

## 故障处理

### 存储切换

当配置为 `auto` 模式时，如果主存储不可用，系统会自动切换到备用存储：

1. HBase 不可用 → 自动切换到 MySQL
2. MySQL 不可用 → 自动切换到 HBase

### 错误处理

- 表不存在：返回空结果
- 查询超时：返回错误信息
- 数据格式错误：记录日志并跳过异常数据

## 设备类型转换

### 类型映射关系

在控制器中，需要将 `Device.getType()` 返回的 Integer 类型转换为字符串类型：

```java
// 设备类型转换示例
DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
if (deviceTypeEnum == null) {
    return AjaxResult.error("不支持的设备类型");
}
String deviceType = deviceTypeEnum.getAlias().toLowerCase();
```

### 支持的设备类型映射

| Integer 值 | 设备类型枚举 | 字符串别名 | 说明 |
|-----------|------------|-----------|------|
| 1 | GPS | gps | GPS定位设备 |
| 2 | AWS | aws | 自动气象站 |
| 3 | MODEM | modem | 卫星猫设备 |
| 4 | PDU | pdu | 电源分配单元 |
| 5 | ATTITUDE | attitude | 姿态仪设备 |
| 6 | AMPLIFIER | amplifier | 功放设备 |

## 扩展功能

### 1. 批量查询

支持一次查询多个设备的最新数据：

```bash
GET /business/data/unified/batch/latest?deviceIds=1,2,3,4,5
```

### 2. 数据统计

提供详细的数据统计信息，包括不同时间间隔的数据量对比：

```bash
GET /business/data/unified/statistics?deviceId=1
```

### 3. 存储状态监控

实时查看各存储类型的可用状态：

```bash
GET /business/data/unified/storage/status
```

## 后续计划

1. 添加数据同步功能，支持 MySQL 和 HBase 之间的数据同步
2. 实现查询结果缓存，提高重复查询的性能
3. 添加数据压缩和归档功能
4. 支持更多的查询条件和聚合统计
