<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataGpsMapper">
    
    <resultMap type="BuDataGps" id="BuDataGpsResult">
        <result property="id"    column="id"    />
        <result property="initialTime"    column="initial_time"    />
        <result property="initialBjTime"    column="initial_bj_time"    />
        <result property="utcTime"    column="utc_time"    />
        <result property="latitudeHemisphere"    column="latitude_hemisphere"    />
        <result property="longitudeHemisphere"    column="longitude_hemisphere"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
    </resultMap>

    <sql id="selectBuDataGpsVo">
        select id, initial_time, initial_bj_time, utc_time, latitude_hemisphere, longitude_hemisphere, latitude, longitude from bu_data_gps
    </sql>

    <select id="selectBuDataGpsList" parameterType="BuDataGps" resultMap="BuDataGpsResult">
        <include refid="selectBuDataGpsVo"/>
        <where>  
            <if test="initialTime != null "> and initial_time = #{initialTime}</if>
            <if test="initialBjTime != null "> and initial_bj_time = #{initialBjTime}</if>
            <if test="utcTime != null  and utcTime != ''"> and utc_time = #{utcTime}</if>
            <if test="latitudeHemisphere != null  and latitudeHemisphere != ''"> and latitude_hemisphere = #{latitudeHemisphere}</if>
            <if test="longitudeHemisphere != null  and longitudeHemisphere != ''"> and longitude_hemisphere = #{longitudeHemisphere}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
        </where>
    </select>
    
    <select id="selectBuDataGpsById" parameterType="Long" resultMap="BuDataGpsResult">
        <include refid="selectBuDataGpsVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataGps" parameterType="BuDataGps" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_gps
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="utcTime != null">utc_time,</if>
            <if test="latitudeHemisphere != null">latitude_hemisphere,</if>
            <if test="longitudeHemisphere != null">longitude_hemisphere,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="utcTime != null">#{utcTime},</if>
            <if test="latitudeHemisphere != null">#{latitudeHemisphere},</if>
            <if test="longitudeHemisphere != null">#{longitudeHemisphere},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
         </trim>
    </insert>

    <update id="updateBuDataGps" parameterType="BuDataGps">
        update bu_data_gps
        <trim prefix="SET" suffixOverrides=",">
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="utcTime != null">utc_time = #{utcTime},</if>
            <if test="latitudeHemisphere != null">latitude_hemisphere = #{latitudeHemisphere},</if>
            <if test="longitudeHemisphere != null">longitude_hemisphere = #{longitudeHemisphere},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataGpsById" parameterType="Long">
        delete from bu_data_gps where id = #{id}
    </delete>

    <delete id="deleteBuDataGpsByIds" parameterType="String">
        delete from bu_data_gps where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>