package com.snct.system.domain.data;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * 功放数据对象 bu_data_amplifier
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public class BuDataAmplifier extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 录入时间戳 */
    @Excel(name = "录入时间戳")
    private Long initialTime;

    /** 录入时间(北京时间) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "录入时间(北京时间)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /** 衰减值 */
    @Excel(name = "衰减值")
    private String decay;

    /** 温度 */
    @Excel(name = "温度")
    private String temp;

    /** 输出功率 */
    @Excel(name = "输出功率")
    private String outPower;

    /** 设备状态 */
    @Excel(name = "设备状态")
    private String status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setInitialTime(Long initialTime) 
    {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() 
    {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) 
    {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() 
    {
        return initialBjTime;
    }

    public void setDecay(String decay) 
    {
        this.decay = decay;
    }

    public String getDecay() 
    {
        return decay;
    }

    public void setTemp(String temp) 
    {
        this.temp = temp;
    }

    public String getTemp() 
    {
        return temp;
    }

    public void setOutPower(String outPower) 
    {
        this.outPower = outPower;
    }

    public String getOutPower() 
    {
        return outPower;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("initialTime", getInitialTime())
            .append("initialBjTime", getInitialBjTime())
            .append("decay", getDecay())
            .append("temp", getTemp())
            .append("outPower", getOutPower())
            .append("status", getStatus())
            .toString();
    }
}
