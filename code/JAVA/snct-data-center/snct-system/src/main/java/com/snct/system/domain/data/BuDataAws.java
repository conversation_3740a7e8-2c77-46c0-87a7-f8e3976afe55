package com.snct.system.domain.data;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * AWS气象数据对象 bu_data_aws
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public class BuDataAws extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 录入时间戳 */
    @Excel(name = "录入时间戳")
    private Long initialTime;

    /** 录入时间(北京时间) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "录入时间(北京时间)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /** 相对风向 */
    @Excel(name = "相对风向")
    private String relativeWind;

    /** 相对风速 */
    @Excel(name = "相对风速")
    private String relativeWindSpeed;

    /** 气温值 */
    @Excel(name = "气温值")
    private String airTemperature;

    /** 相对湿度数值 */
    @Excel(name = "相对湿度数值")
    private String humidity;

    /** 露点温度数值 */
    @Excel(name = "露点温度数值")
    private String pointTem;

    /** 气压数值 */
    @Excel(name = "气压数值")
    private String pressure;

    /** QFE气压数值 */
    @Excel(name = "QFE气压数值")
    private String qfe;

    /** QNH气压数值 */
    @Excel(name = "QNH气压数值")
    private String qnh;

    /** DP温度 */
    @Excel(name = "DP温度")
    private String dp;

    /** UTC时间 */
    @Excel(name = "UTC时间")
    private String utcTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setInitialTime(Long initialTime) 
    {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() 
    {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) 
    {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() 
    {
        return initialBjTime;
    }

    public void setRelativeWind(String relativeWind) 
    {
        this.relativeWind = relativeWind;
    }

    public String getRelativeWind() 
    {
        return relativeWind;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) 
    {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getRelativeWindSpeed() 
    {
        return relativeWindSpeed;
    }

    public void setAirTemperature(String airTemperature) 
    {
        this.airTemperature = airTemperature;
    }

    public String getAirTemperature() 
    {
        return airTemperature;
    }

    public void setHumidity(String humidity) 
    {
        this.humidity = humidity;
    }

    public String getHumidity() 
    {
        return humidity;
    }

    public void setPointTem(String pointTem) 
    {
        this.pointTem = pointTem;
    }

    public String getPointTem() 
    {
        return pointTem;
    }

    public void setPressure(String pressure) 
    {
        this.pressure = pressure;
    }

    public String getPressure() 
    {
        return pressure;
    }

    public void setQfe(String qfe) 
    {
        this.qfe = qfe;
    }

    public String getQfe() 
    {
        return qfe;
    }

    public void setQnh(String qnh) 
    {
        this.qnh = qnh;
    }

    public String getQnh() 
    {
        return qnh;
    }

    public void setDp(String dp) 
    {
        this.dp = dp;
    }

    public String getDp() 
    {
        return dp;
    }

    public void setUtcTime(String utcTime) 
    {
        this.utcTime = utcTime;
    }

    public String getUtcTime() 
    {
        return utcTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("initialTime", getInitialTime())
            .append("initialBjTime", getInitialBjTime())
            .append("relativeWind", getRelativeWind())
            .append("relativeWindSpeed", getRelativeWindSpeed())
            .append("airTemperature", getAirTemperature())
            .append("humidity", getHumidity())
            .append("pointTem", getPointTem())
            .append("pressure", getPressure())
            .append("qfe", getQfe())
            .append("qnh", getQnh())
            .append("dp", getDp())
            .append("utcTime", getUtcTime())
            .toString();
    }
}
