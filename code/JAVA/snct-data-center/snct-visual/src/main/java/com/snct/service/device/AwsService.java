package com.snct.service.device;

import com.snct.dctcore.commoncore.domain.hbase.AwsHbaseVo;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;

import com.snct.system.service.DeviceDataQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName: AwsService
 * @Description: 气象站数据服务
 * @author: wzewei
 * @date: 2025-08-15 09:21
 */
@Service
@Transactional(readOnly = true)
public class AwsService {

    private final Logger logger = LoggerFactory.getLogger(AwsService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;
    @Autowired
    private DeviceDataQueryService deviceDataQueryService;


    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<AwsHbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.AWS.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new AwsHbaseVo(), tableName, rowList);
    }

    public AwsHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.AWS.getAlias(), deviceCode,100);
        return hBaseDaoUtil.getLatestRow(new AwsHbaseVo(), tableName);
    }

    /**
     * 从MySQL数据库按时间范围查询AWS数据
     * 注意：MySQL存储的是北斗数据，本身就有时间间隔，不需要抽稀处理
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型字符串（如"aws"）
     * @param interval 时间间隔（保留参数，北斗数据不需要抽稀，直接按时间范围查询）
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return AWS数据列表
     */
    public List<AwsHbaseVo> queryByTimeFromMysql(String sn, String deviceCode, String deviceType,
                                                 Integer interval, Long startTime, Long endTime) {
        try {
            logger.debug("从MySQL查询AWS数据: sn={}, deviceCode={}, deviceType={}, startTime={}, endTime={}",
                        sn, deviceCode, deviceType, startTime, endTime);

            // 北斗数据本身就有时间间隔，直接按时间范围查询所有数据
            List<Map<String, Object>> dataList = deviceDataQueryService.queryDeviceDataByTimeRange(sn, deviceCode, deviceType, startTime, endTime);

            if (dataList == null || dataList.isEmpty()) {
                logger.warn("MySQL查询AWS数据为空: sn={}, deviceCode={}", sn, deviceCode);
                return new ArrayList<>();
            }

            // 将MySQL查询结果转换为AwsHbaseVo对象
            List<AwsHbaseVo> awsDataList = dataList.stream()
                .map(this::convertMysqlDataToAwsHbaseVo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            logger.debug("MySQL查询AWS数据成功，返回{}条记录", awsDataList.size());
            return awsDataList;

        } catch (Exception e) {
            logger.error("从MySQL查询AWS数据失败: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 从MySQL数据库获取AWS设备的最新一条数据
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型字符串（如"aws"）
     * @return AWS最新数据
     */
    public AwsHbaseVo getLatestDataFromMysql(String sn, String deviceCode, String deviceType) {
        try {
            logger.debug("从MySQL获取AWS最新数据: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType);

            Map<String, Object> latestData = deviceDataQueryService.getLatestDeviceDataCompatible(sn, deviceCode, deviceType);

            if (latestData == null) {
                logger.warn("MySQL中未找到AWS最新数据: sn={}, deviceCode={}", sn, deviceCode);
                return null;
            }

            AwsHbaseVo awsData = convertMysqlDataToAwsHbaseVo(latestData);

            if (awsData != null) {
                logger.debug("MySQL获取AWS最新数据成功: sn={}, deviceCode={}, time={}",
                           sn, deviceCode, awsData.getInitialBjTime());
            }

            return awsData;

        } catch (Exception e) {
            logger.error("从MySQL获取AWS最新数据失败: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType, e);
            return null;
        }
    }

    /**
     * 将MySQL查询结果转换为AwsHbaseVo对象
     *
     * @param mysqlData MySQL查询结果
     * @return AwsHbaseVo对象
     */
    private AwsHbaseVo convertMysqlDataToAwsHbaseVo(Map<String, Object> mysqlData) {
        if (mysqlData == null) {
            return null;
        }

        try {
            AwsHbaseVo awsVo = new AwsHbaseVo();

            // 设置基础字段
            awsVo.setId(getString(mysqlData, "id"));
            awsVo.setInitialTime(getString(mysqlData, "initialTime"));
            awsVo.setInitialBjTime(getString(mysqlData, "initialBjTime"));
            //awsVo.setUtcTime(getString(mysqlData, "utcTime"));

            // 设置AWS特有字段
            awsVo.setRelativeWind(getString(mysqlData, "relativeWind"));
            awsVo.setRelativeWindSpeed(getString(mysqlData, "relativeWindSpeed"));
            awsVo.setAirTemperature(getString(mysqlData, "airTemperature"));
            awsVo.setHumidity(getString(mysqlData, "humidity"));
            awsVo.setPointTem(getString(mysqlData, "pointTem"));
            awsVo.setPressure(getString(mysqlData, "pressure"));
            //awsVo.set(getString(mysqlData, "qfe"));
            //awsVo.setQnh(getString(mysqlData, "qnh"));
            //awsVo.setDp(getString(mysqlData, "dp"));

            return awsVo;

        } catch (Exception e) {
            logger.error("转换MySQL数据到AwsHbaseVo失败", e);
            return null;
        }
    }

    /**
     * 安全获取字符串值
     *
     * @param map 数据映射
     * @param key 键名
     * @return 字符串值
     */
    private String getString(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }

}
