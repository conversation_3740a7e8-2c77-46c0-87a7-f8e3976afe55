package com.snct.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDataAmplifierMapper;
import com.snct.system.domain.data.BuDataAmplifier;
import com.snct.system.service.IBuDataAmplifierService;

/**
 * 功放数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
@Service
public class BuDataAmplifierServiceImpl implements IBuDataAmplifierService 
{
    @Autowired
    private BuDataAmplifierMapper buDataAmplifierMapper;

    /**
     * 查询功放数据
     * 
     * @param id 功放数据主键
     * @return 功放数据
     */
    @Override
    public BuDataAmplifier selectBuDataAmplifierById(Long id)
    {
        return buDataAmplifierMapper.selectBuDataAmplifierById(id);
    }

    /**
     * 查询功放数据列表
     * 
     * @param buDataAmplifier 功放数据
     * @return 功放数据
     */
    @Override
    public List<BuDataAmplifier> selectBuDataAmplifierList(BuDataAmplifier buDataAmplifier)
    {
        return buDataAmplifierMapper.selectBuDataAmplifierList(buDataAmplifier);
    }

    /**
     * 新增功放数据
     * 
     * @param buDataAmplifier 功放数据
     * @return 结果
     */
    @Override
    public int insertBuDataAmplifier(BuDataAmplifier buDataAmplifier)
    {
        return buDataAmplifierMapper.insertBuDataAmplifier(buDataAmplifier);
    }

    /**
     * 修改功放数据
     * 
     * @param buDataAmplifier 功放数据
     * @return 结果
     */
    @Override
    public int updateBuDataAmplifier(BuDataAmplifier buDataAmplifier)
    {
        return buDataAmplifierMapper.updateBuDataAmplifier(buDataAmplifier);
    }

    /**
     * 批量删除功放数据
     * 
     * @param ids 需要删除的功放数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataAmplifierByIds(Long[] ids)
    {
        return buDataAmplifierMapper.deleteBuDataAmplifierByIds(ids);
    }

    /**
     * 删除功放数据信息
     * 
     * @param id 功放数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataAmplifierById(Long id)
    {
        return buDataAmplifierMapper.deleteBuDataAmplifierById(id);
    }
}
