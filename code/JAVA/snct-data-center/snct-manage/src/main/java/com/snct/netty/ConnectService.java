package com.snct.netty;

import com.snct.common.utils.spring.SpringUtils;
import com.snct.dctcore.commoncore.domain.transfer.TransferPackage;
import com.snct.dctcore.commoncore.enums.PackageTypeEnum;
import com.snct.dctcore.commoncore.utils.JsonUtil;
import com.snct.dctcore.commoncore.utils.NumUtils;
import com.snct.system.service.DeviceDataMysqlService;
import com.snct.system.service.DynamicTableService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName: ConnectService
 * @Description: udp连接服务
 * @author: wzewei
 * @date: 2025-09-03 10:30
 */
@Service
public class ConnectService {

    private Logger logger = LoggerFactory.getLogger(ConnectService.class);
    /**
     * 岸上UPD监听 端口
     */
    @Value("${data-center.udpPort}")
    private Integer udpPort;

    @Resource
    private DataHandleService dataHandleService;

    @Autowired
    private SendService sendService;

    @Autowired
    private DeviceDataMysqlService deviceDataMysqlService;

    @Autowired
    private DynamicTableService dynamicTableService;

    /**
     * 创建UDP接收服务
     */
    public void initUdpServer() {
        TaskExecutor threadPool = SpringUtils.getBean("threadPoolTaskExecutor");
        threadPool.execute(() -> {
            new UdpServer(udpPort);
        });
    }


    /**
     * 处理接收到的消息
     *
     * @param transferPackage
     */
    public void handleMessage(TransferPackage transferPackage) {
        try {
            if (transferPackage == null) {
                return;
            }
            // 保持连接的信息,不做处理
            if (PackageTypeEnum.CONNECT_KEEP.getValue().equals(transferPackage.getPackageType())) {
                logger.info("接到数据,--保持连接---{}", JsonUtil.obj2String(transferPackage));
                return;
            }

            // 处理传感器数据
            if (PackageTypeEnum.DEVICE_DATA.getValue().equals(transferPackage.getPackageType())) {
                if (dataHandleService.removeNonExistData(transferPackage)) {
                    return;
                }
                String[] sns = transferPackage.getSn().split(",");

                // 北斗链路Bc1cfr,网络链路Bc2cfr
                String sendType = sns[0];
                System.out.println("发送类型：" + sendType);

                String beidouNm = sns[1];//北斗的编号
                if (beidouNm.equalsIgnoreCase("1111111")) {
                    return;
                }

                transferPackage.setSn(beidouNm);//真正的SN
                if (beidouNm.equalsIgnoreCase("8888888")) {
                    //第三方无用数据
                    return;
                } else {
                    //根据编号以及设备编码查找设备表是否已经存在
                    String deviceType = getDeviceTypeAlias(transferPackage.getDeviceType());
                    if (deviceType != null) {
                        String tableName = dynamicTableService.generateTableName(
                                beidouNm,
                                transferPackage.getDeviceCode(),
                                deviceType
                        );

                        // 检查表是否存在，如果不存在则创建
                        if (!dynamicTableService.tableExists(tableName)) {
                            boolean created = dynamicTableService.createDeviceTable(
                                    beidouNm,
                                    transferPackage.getDeviceCode(),
                                    deviceType
                            );
                            if (created) {
                                logger.info("成功创建设备数据表: {}", tableName);
                            } else {
                                logger.error("强制重新创建设备数据表失败: {}", tableName);

                            }
                        }

                        // 保存数据到MySQL
                        boolean saved = deviceDataMysqlService.saveDeviceData(transferPackage);
                        if (saved) {
                            logger.debug("设备数据已保存到MySQL表: {}", tableName);
                        } else {
                            logger.warn("设备数据保存到MySQL失败: {}", tableName);
                        }
                    }
                }

                //如果存在则往下走
                dataHandleService.receiveMessage(transferPackage);
            }

        } catch (Exception e) {
            logger.error("处理数据失败，--{}", e);
        }
    }

    /**
     * 解析传输的UDP包
     *
     * @param msg
     * @return
     */
    public TransferPackage analysisPackage(byte[] msg) throws Exception {
        int length = msg.length;
        if (length < 28) {
            return null;
        }

        TransferPackage transferPackage = new TransferPackage();

        int offset = 0;

        // 船只序列号
        transferPackage.setSn(new String(NumUtils.splitBytes(msg, offset, 14)));
        offset += 14;

        // 设备类型
        transferPackage.setPackageType((int) NumUtils.splitByte(msg, offset));
        offset++;

        // 统一编码
        transferPackage.setCommandNum(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
        offset += 4;

        //时间戳
        transferPackage.setTime(NumUtils.bytesToLong(NumUtils.splitBytes(msg, offset, 8)));
        offset += 8;

        // 是否补发
        transferPackage.setIsRepair((int) NumUtils.splitByte(msg, offset));
        offset++;

        if (PackageTypeEnum.DEVICE_DATA.getValue().equals(transferPackage.getPackageType())) {
            // 设备类型
            transferPackage.setDeviceType((int) NumUtils.splitByte(msg, offset));
            offset++;
        }

        // 设备编码
        transferPackage.setDeviceCode(new String(NumUtils.splitBytes(msg, offset, 4)));
        offset += 4;

        if (PackageTypeEnum.SNAPSHOT_DATA.getValue().equals(transferPackage.getPackageType())) {
            // 拆包总数
            transferPackage.setUnpackingTotal(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
            offset += 4;

            // 拆包编号
            transferPackage.setUnpackingNum(NumUtils.byteArray2Int(NumUtils.splitBytes(msg, offset, 4)));
            offset += 4;
        }

        // 消息主体
        transferPackage.setMessage(new String(NumUtils.splitBytes(msg, offset, length - offset)));

        return transferPackage;
    }

    /**
     * 根据设备类型获取设备类型别名
     *
     * @param deviceType 设备类型
     * @return 设备类型别名
     */
    private String getDeviceTypeAlias(Integer deviceType) {
        if (deviceType == null) {
            return null;
        }

        try {
            com.snct.dctcore.commoncore.enums.DeviceTypeEnum deviceTypeEnum =
                    com.snct.dctcore.commoncore.enums.DeviceTypeEnum.getByValue(deviceType);
            return deviceTypeEnum != null ? deviceTypeEnum.getAlias().toLowerCase() : null;
        } catch (Exception e) {
            logger.warn("获取设备类型别名失败: {}", e.getMessage());
            return null;
        }
    }

}