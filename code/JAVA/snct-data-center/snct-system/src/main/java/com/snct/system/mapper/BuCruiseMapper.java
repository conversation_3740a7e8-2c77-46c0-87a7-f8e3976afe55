package com.snct.system.mapper;

import java.util.List;
import com.snct.system.domain.BuCruise;

/**
 * 航次Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface BuCruiseMapper 
{
    /**
     * 查询航次
     * 
     * @param cruiseId 航次主键
     * @return 航次
     */
    public BuCruise selectBuCruiseByCruiseId(Long cruiseId);

    /**
     * 查询航次列表
     * 
     * @param buCruise 航次
     * @return 航次集合
     */
    public List<BuCruise> selectBuCruiseList(BuCruise buCruise);
    public BuCruise selectNewCruise(BuCruise buCruise);

    /**
     * 新增航次
     * 
     * @param buCruise 航次
     * @return 结果
     */
    public int insertBuCruise(BuCruise buCruise);

    /**
     * 修改航次
     * 
     * @param buCruise 航次
     * @return 结果
     */
    public int updateBuCruise(BuCruise buCruise);

    /**
     * 删除航次
     * 
     * @param cruiseId 航次主键
     * @return 结果
     */
    public int deleteBuCruiseByCruiseId(Long cruiseId);

    /**
     * 批量删除航次
     * 
     * @param cruiseIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuCruiseByCruiseIds(Long[] cruiseIds);
}
