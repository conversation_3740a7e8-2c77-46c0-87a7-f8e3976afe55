package com.snct.web.controller.business;


import com.google.common.collect.Lists;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.page.TableDataInfo;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.snct.service.DeviceDataService;
import com.snct.system.domain.Device;
import com.snct.system.service.IDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * @ClassName: BuDeviceDataController
 * @Description: 设备数据控制类
 * @author: wzewei
 * @date: 2025-08-25 09:36
 */
@Api("设备数据管理")
@RestController
@RequestMapping("/business/data")
public class BuDeviceDataController extends BaseController {

    @Autowired
    private HBaseDaoUtil hbaseDaoUtil;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private DeviceDataService deviceDataService;

    @ApiOperation("分页查询设备数据列表")
    @GetMapping("/hbase/list")
    public TableDataInfo query(
            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @RequestParam(required = false) Integer interval,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页记录数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("排序方式") @RequestParam(defaultValue = "desc") String sortOrder) {

        Device device = deviceService.selectDeviceById(deviceId);

        if (device == null) {
            return new TableDataInfo(new ArrayList<>(), 0);
        }

        DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
        if (deviceTypeEnum == null) {
            return getDataTable(Lists.newArrayList(), 0);
        }

        String tableName = hbaseDaoUtil.getTableName(device.getSn(), deviceTypeEnum.getAlias(), device.getCode(),
                interval);
        Map<String, Object> queryResult = deviceDataService.queryDeviceDataPage(deviceTypeEnum.getValue(), tableName,
                startTime, endTime, pageNum,
                pageSize, sortOrder);

        return getDataTable((List<?>) queryResult.get("data"), Long.parseLong(queryResult.get("total").toString()));
    }

    /**
     * 根据rowkey查询设备详细数据
     *
     * @param deviceId 设备ID
     * @param interval 数据间隔
     * @param rowkeys  rowkey列表，多个用逗号分隔
     * @return 设备详细数据列表
     */
    @ApiOperation("根据rowkey查询设备详细数据")
    @GetMapping("/hbase/details")
    public AjaxResult getDetailsByRowKeys(
            @ApiParam(value = "设备ID", required = true) @RequestParam Long deviceId,
            @ApiParam(value = "数据间隔", example = "100") @RequestParam(defaultValue = "100") Integer interval,
            @ApiParam(value = "rowkey列表，多个用逗号分隔", required = true) @RequestParam String rowkeys) {

        if (deviceId == null) {
            return AjaxResult.error("设备ID不能为空");
        }

        if (StringUtils.isBlank(rowkeys)) {
            return AjaxResult.error("rowkey不能为空");
        }

        try {
            Device device = deviceService.selectDeviceById(deviceId);
            if (device == null) {
                return AjaxResult.error("设备不存在");
            }

            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
            if (deviceTypeEnum == null) {
                return AjaxResult.error("不支持的设备类型");
            }

            String tableName = hbaseDaoUtil.getTableName(device.getSn(), deviceTypeEnum.getAlias(), device.getCode(), interval);

            String[] rowkeyArray = rowkeys.split(",");
            for (int i = 0; i < rowkeyArray.length; i++) {
                rowkeyArray[i] = rowkeyArray[i].trim();
            }

            List<Object> detailsList = deviceDataService.getDeviceDetailsByRowKeys(tableName, deviceTypeEnum.getValue(), rowkeyArray);

            return AjaxResult.success("查询成功", detailsList);
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

}
