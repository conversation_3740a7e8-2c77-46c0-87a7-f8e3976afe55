<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataAwsMapper">
    
    <resultMap type="BuDataAws" id="BuDataAwsResult">
        <result property="id"    column="id"    />
        <result property="initialTime"    column="initial_time"    />
        <result property="initialBjTime"    column="initial_bj_time"    />
        <result property="relativeWind"    column="relative_wind"    />
        <result property="relativeWindSpeed"    column="relative_wind_speed"    />
        <result property="airTemperature"    column="air_temperature"    />
        <result property="humidity"    column="humidity"    />
        <result property="pointTem"    column="point_tem"    />
        <result property="pressure"    column="pressure"    />
        <result property="qfe"    column="qfe"    />
        <result property="qnh"    column="qnh"    />
        <result property="dp"    column="dp"    />
        <result property="utcTime"    column="utc_time"    />
    </resultMap>

    <sql id="selectBuDataAwsVo">
        select id, initial_time, initial_bj_time, relative_wind, relative_wind_speed, air_temperature, humidity, point_tem, pressure, qfe, qnh, dp, utc_time from bu_data_aws
    </sql>

    <select id="selectBuDataAwsList" parameterType="BuDataAws" resultMap="BuDataAwsResult">
        <include refid="selectBuDataAwsVo"/>
        <where>  
            <if test="initialTime != null "> and initial_time = #{initialTime}</if>
            <if test="initialBjTime != null "> and initial_bj_time = #{initialBjTime}</if>
            <if test="relativeWind != null  and relativeWind != ''"> and relative_wind = #{relativeWind}</if>
            <if test="relativeWindSpeed != null  and relativeWindSpeed != ''"> and relative_wind_speed = #{relativeWindSpeed}</if>
            <if test="airTemperature != null  and airTemperature != ''"> and air_temperature = #{airTemperature}</if>
            <if test="humidity != null  and humidity != ''"> and humidity = #{humidity}</if>
            <if test="pointTem != null  and pointTem != ''"> and point_tem = #{pointTem}</if>
            <if test="pressure != null  and pressure != ''"> and pressure = #{pressure}</if>
            <if test="qfe != null  and qfe != ''"> and qfe = #{qfe}</if>
            <if test="qnh != null  and qnh != ''"> and qnh = #{qnh}</if>
            <if test="dp != null  and dp != ''"> and dp = #{dp}</if>
            <if test="utcTime != null  and utcTime != ''"> and utc_time = #{utcTime}</if>
        </where>
    </select>
    
    <select id="selectBuDataAwsById" parameterType="Long" resultMap="BuDataAwsResult">
        <include refid="selectBuDataAwsVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataAws" parameterType="BuDataAws" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_aws
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="relativeWind != null">relative_wind,</if>
            <if test="relativeWindSpeed != null">relative_wind_speed,</if>
            <if test="airTemperature != null">air_temperature,</if>
            <if test="humidity != null">humidity,</if>
            <if test="pointTem != null">point_tem,</if>
            <if test="pressure != null">pressure,</if>
            <if test="qfe != null">qfe,</if>
            <if test="qnh != null">qnh,</if>
            <if test="dp != null">dp,</if>
            <if test="utcTime != null">utc_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="relativeWind != null">#{relativeWind},</if>
            <if test="relativeWindSpeed != null">#{relativeWindSpeed},</if>
            <if test="airTemperature != null">#{airTemperature},</if>
            <if test="humidity != null">#{humidity},</if>
            <if test="pointTem != null">#{pointTem},</if>
            <if test="pressure != null">#{pressure},</if>
            <if test="qfe != null">#{qfe},</if>
            <if test="qnh != null">#{qnh},</if>
            <if test="dp != null">#{dp},</if>
            <if test="utcTime != null">#{utcTime},</if>
         </trim>
    </insert>

    <update id="updateBuDataAws" parameterType="BuDataAws">
        update bu_data_aws
        <trim prefix="SET" suffixOverrides=",">
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="relativeWind != null">relative_wind = #{relativeWind},</if>
            <if test="relativeWindSpeed != null">relative_wind_speed = #{relativeWindSpeed},</if>
            <if test="airTemperature != null">air_temperature = #{airTemperature},</if>
            <if test="humidity != null">humidity = #{humidity},</if>
            <if test="pointTem != null">point_tem = #{pointTem},</if>
            <if test="pressure != null">pressure = #{pressure},</if>
            <if test="qfe != null">qfe = #{qfe},</if>
            <if test="qnh != null">qnh = #{qnh},</if>
            <if test="dp != null">dp = #{dp},</if>
            <if test="utcTime != null">utc_time = #{utcTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataAwsById" parameterType="Long">
        delete from bu_data_aws where id = #{id}
    </delete>

    <delete id="deleteBuDataAwsByIds" parameterType="String">
        delete from bu_data_aws where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>