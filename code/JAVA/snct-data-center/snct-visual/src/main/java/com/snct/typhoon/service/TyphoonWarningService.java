package com.snct.typhoon.service;

import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.system.domain.BuShipWarning;
import com.snct.system.domain.Device;
import com.snct.system.domain.Ship;
import com.snct.system.service.IBuShipWarningService;
import com.snct.system.service.IDeviceService;
import com.snct.system.service.IShipService;
import com.snct.typhoon.constants.TyphoonWarningLevel;
import com.snct.typhoon.domain.TyphoonShipWarning;
import com.snct.typhoon.domain.vo.ActiveTyphoonSummary;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.geo.*;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.GeoOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.alibaba.fastjson.JSONObject;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: TyphoonWarningService
 * @Description: 台风预警服务
 * @author: wzewei
 * @date: 2025-08-22 11:41
 */
@Service
public class TyphoonWarningService {

    private static final Logger logger = LoggerFactory.getLogger(TyphoonWarningService.class);

    /**
     * 预警去重时间窗口（毫秒）- 1小时
     */
    private static final long WARNING_DEDUP_WINDOW = 3600000L;

    @Autowired
    private IBuShipWarningService buShipWarningService;

    @Autowired
    private IShipService shipService;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 执行台风预警检查并保存到数据库
     */
    public void checkAndSaveTyphoonWarnings() {
        try {
            // 1. 获取活跃台风
            List<ActiveTyphoonSummary> activeTyphoons = getActiveTyphoons();

            if (activeTyphoons.isEmpty()) {
                return;
            }

            // 2. 对每个台风进行预警检查
            for (ActiveTyphoonSummary typhoon : activeTyphoons) {
                try {
                    List<TyphoonShipWarning> warnings = checkTyphoonWarnings(typhoon);

                    if (!warnings.isEmpty()) {
                        // 3. 保存预警记录到数据库
                        saveTyphoonWarnings(warnings, typhoon);
                    }
                } catch (Exception e) {
                    logger.error("处理台风 {} 预警时发生错误", typhoon.getName(), e);
                }
            }

        } catch (Exception e) {
            logger.error("台风预警检查失败", e);
        }
    }

    /**
     * 检查单个台风的预警情况
     */
    public List<TyphoonShipWarning> checkTyphoonWarnings(ActiveTyphoonSummary typhoon) {
        List<TyphoonShipWarning> warnings = new ArrayList<>();

        try {
            // 验证台风坐标
            if (!StringUtils.hasText(typhoon.getCenterLng()) ||
                    !StringUtils.hasText(typhoon.getCenterLat())) {
                return warnings;
            }

            double typhoonLng = Double.parseDouble(typhoon.getCenterLng());
            double typhoonLat = Double.parseDouble(typhoon.getCenterLat());

            // 使用Redis GEO命令查询预警范围内的船舶
            GeoOperations<String, String> geoOps = redisTemplate.opsForGeo();

            // 创建距离对象
            Distance maxDistance = new Distance(TyphoonWarningLevel.getMaxRadius(), Metrics.KILOMETERS);

            // 执行GEORADIUS查询 - 使用经纬度坐标作为中心点
            String geoKey = RedisParameter.SHIP_LOCATION_GEO;
            Point typhoonCenter = new Point(typhoonLng, typhoonLat);
            Circle searchArea = new Circle(typhoonCenter, maxDistance);
            GeoResults<RedisGeoCommands.GeoLocation<String>> results =
                    geoOps.radius(
                            geoKey,
                            searchArea,
                            RedisGeoCommands.GeoRadiusCommandArgs.newGeoRadiusArgs()
                                    .includeDistance()
                                    .includeCoordinates()
                                    .sortAscending()
                    );

            if (results == null || results.getContent().isEmpty()) {
                return warnings;
            }

            // 处理查询结果
            for (GeoResult<RedisGeoCommands.GeoLocation<String>> result : results) {
                try {
                    String name = result.getContent().getName();
                    String[] split = name.split("#");
                    String shipSn = split[0];
                    String deviceCode = split[1];
                    double distance = result.getDistance().getValue();
                    Point shipLocation = result.getContent().getPoint();

                    // 确定预警级别
                    TyphoonWarningLevel warningLevel = TyphoonWarningLevel.determineByDistance(distance);
                    if (warningLevel != null) {
                        TyphoonShipWarning warning = new TyphoonShipWarning(
                                shipSn, deviceCode, null, distance, warningLevel, shipLocation,
                                typhoon.getTfid(), typhoon.getName(), typhoonCenter
                        );
                        warnings.add(warning);
                    }
                } catch (Exception e) {
                    logger.error("处理船舶预警信息时发生错误", e);
                }
            }

        } catch (Exception e) {
            logger.error("检查台风 {} 预警时发生错误", typhoon.getName(), e);
        }

        return warnings;
    }

    /**
     * 保存台风预警记录到数据库
     */
    public int saveTyphoonWarnings(List<TyphoonShipWarning> warnings, ActiveTyphoonSummary typhoon) {
        int savedCount = 0;

        for (TyphoonShipWarning warning : warnings) {
            try {
                // 检查是否已存在相同的预警记录（避免重复预警）
                if (isWarningAlreadyExists(warning.getShipSn(), typhoon.getTfid(),
                        warning.getLevel())) {
                    continue;
                }

                // 获取船舶信息
                Ship ship = shipService.selectShipByShipSn(warning.getShipSn());
                if (ship == null) {
                    continue;
                }

                // 获取设备信息
                Device device = deviceService.selectDeviceBySnAndCode(warning.getShipSn(), warning.getDeviceCode());

                // 设置船舶名称
                warning.setShipName(ship.getName());

                // 构建预警记录
                BuShipWarning warningRecord = createWarningRecord(warning, ship, device, typhoon);

                // 保存到数据库
                buShipWarningService.insertBuShipWarning(warningRecord);
                savedCount++;

                logger.info("保存台风预警记录: 船舶={}({}), 台风={}, 距离={}km, 级别={}",
                        ship.getName(), warning.getShipSn(), typhoon.getName(),
                        warning.getDistance(), warning.getLevel().getDescription());

            } catch (Exception e) {
                logger.error("保存预警记录失败: 船舶={}, 台风={}",
                        warning.getShipSn(), typhoon.getName(), e);
            }
        }

        // 保存预警信息到Redis
        if (savedCount > 0) {
            saveTyphoonWarningsToRedis(warnings, typhoon);
        }

        return savedCount;
    }

    /**
     * 保存台风预警信息到Redis
     */
    private void saveTyphoonWarningsToRedis(List<TyphoonShipWarning> warnings, ActiveTyphoonSummary typhoon) {
        try {
            ValueOperations<String, String> valueOps = redisTemplate.opsForValue();

            // 1. 保存单个预警信息
            for (TyphoonShipWarning warning : warnings) {
                String warningKey = RedisParameter.TYPHOON_WARNING + typhoon.getTfid() + ":" + warning.getShipSn();
                Map<String, Object> warningData = buildWarningData(warning, typhoon);
                valueOps.set(warningKey, JSONObject.toJSONString(warningData), 24, TimeUnit.HOURS);

                // 保存船舶预警信息
                String shipWarningKey = RedisParameter.SHIP_WARNING + warning.getShipSn();
                valueOps.set(shipWarningKey, JSONObject.toJSONString(warningData), 24, TimeUnit.HOURS);
            }

            // 2. 保存预警汇总信息
            saveTyphoonWarningSummaryToRedis();

            logger.info("成功保存{}条台风预警信息到Redis", warnings.size());

        } catch (Exception e) {
            logger.error("保存台风预警信息到Redis失败", e);
        }
    }

    /**
     * 创建预警记录对象
     */
    private BuShipWarning createWarningRecord(TyphoonShipWarning warning, Ship ship, Device device,
                                              ActiveTyphoonSummary typhoon) {
        BuShipWarning warningRecord = new BuShipWarning();

        // 基本信息
        warningRecord.setType(1L); // 台风预警
        warningRecord.setDataKey("TYPHOON-" + warning.getTyphoonId());
        warningRecord.setLevel((long) warning.getLevel().getDbLevel());
        warningRecord.setSn(warning.getShipSn());
        warningRecord.setDeptId(ship.getDeptId());
        warningRecord.setDeviceId(device.getId());
        warningRecord.setStatus(0L);

        // 预警信息
        warningRecord.setName(warning.generateWarningMessage());

        // 备注信息
        String remark = String.format(
                "台风强度:%s, 移动方向:%s, 移动速度:%s, 风力等级:%s",
                typhoon.getStrong() != null ? typhoon.getStrong() : "未知",
                typhoon.getMovedirection() != null ? typhoon.getMovedirection() : "未知",
                typhoon.getMovespeed() != null ? typhoon.getMovespeed() : "未知",
                typhoon.getPower() != null ? typhoon.getPower() : "未知"
        );
        warningRecord.setRemark(remark);

        return warningRecord;
    }

    /**
     * 检查预警是否已存在（防重复）
     */
    private boolean isWarningAlreadyExists(String shipSn, String typhoonId,
                                           TyphoonWarningLevel level) {
        try {
            // 查询最近时间窗口内是否已有相同的预警记录
            BuShipWarning queryCondition = new BuShipWarning();
            queryCondition.setSn(shipSn);
            queryCondition.setType(1L);
            queryCondition.setLevel((long) level.getDbLevel());

            List<BuShipWarning> existingWarnings =
                    buShipWarningService.selectBuShipWarningList(queryCondition);

            // 检查是否有时间窗口内的相同台风预警
            Date windowStart = new Date(System.currentTimeMillis() - WARNING_DEDUP_WINDOW);
            return existingWarnings.stream()
                    .anyMatch(w -> w.getCreateTime() != null &&
                            w.getCreateTime().after(windowStart) &&
                            w.getDataKey() != null &&
                            w.getDataKey().contains("TYPHOON-" + typhoonId));

        } catch (Exception e) {
            logger.error("检查预警重复性时发生错误: 船舶={}, 台风={}", shipSn, typhoonId, e);
            return false;
        }
    }

    /**
     * 获取活跃台风列表
     */
    @SuppressWarnings("unchecked")
    private List<ActiveTyphoonSummary> getActiveTyphoons() {
        try {
            Map<String, Object> activeTyphoonData =
                    (Map<String, Object>) redisTemplate.opsForValue()
                            .get(RedisParameter.TYPHOON_ACTIVE_SUMMARY);

            if (activeTyphoonData != null && activeTyphoonData.containsKey("typhoons")) {
                Object typhoonsObj = activeTyphoonData.get("typhoons");
                if (typhoonsObj instanceof List) {
                    return (List<ActiveTyphoonSummary>) typhoonsObj;
                }
            }
        } catch (Exception e) {
            logger.error("获取活跃台风列表时发生错误", e);
        }

        return new ArrayList<>();
    }

    /**
     * 构建预警数据对象
     */
    private Map<String, Object> buildWarningData(TyphoonShipWarning warning, ActiveTyphoonSummary typhoon) {
        Map<String, Object> data = new HashMap<>();
        data.put("shipSn", warning.getShipSn());
        data.put("shipName", warning.getShipName());
        data.put("deviceCode", warning.getDeviceCode());
        data.put("distance", warning.getDistance());
        data.put("level", warning.getLevel().getDbLevel());
        data.put("levelDesc", warning.getLevel().getDescription());
        data.put("message", warning.generateWarningMessage());
        data.put("typhoonId", warning.getTyphoonId());
        data.put("typhoonName", warning.getTyphoonName());
        data.put("warningTime", warning.getWarningTime());
        data.put("updateTime", System.currentTimeMillis());

        // 位置信息
        if (warning.getShipLocation() != null) {
            data.put("shipLat", warning.getShipLocation().getY());
            data.put("shipLng", warning.getShipLocation().getX());
        }
        if (warning.getTyphoonLocation() != null) {
            data.put("typhoonLat", warning.getTyphoonLocation().getY());
            data.put("typhoonLng", warning.getTyphoonLocation().getX());
        }

        // 台风信息
        data.put("typhoonStrong", typhoon.getStrong());
        data.put("typhoonMoveDirection", typhoon.getMovedirection());
        data.put("typhoonMoveSpeed", typhoon.getMovespeed());
        data.put("typhoonPower", typhoon.getPower());

        return data;
    }

    /**
     * 保存台风预警汇总信息到Redis
     */
    private void saveTyphoonWarningSummaryToRedis() {
        try {
            // 获取所有活跃台风的预警统计
            List<ActiveTyphoonSummary> activeTyphoons = getActiveTyphoons();
            Map<String, Object> summaryData = new HashMap<>();

            int totalWarnings = 0;
            int criticalWarnings = 0;
            int highWarnings = 0;
            int mediumWarnings = 0;

            List<Map<String, Object>> typhoonWarnings = new ArrayList<>();

            for (ActiveTyphoonSummary typhoon : activeTyphoons) {
                List<TyphoonShipWarning> warnings = checkTyphoonWarnings(typhoon);
                if (!warnings.isEmpty()) {
                    Map<String, Object> typhoonData = new HashMap<>();
                    typhoonData.put("typhoonId", typhoon.getTfid());
                    typhoonData.put("typhoonName", typhoon.getName());
                    typhoonData.put("warningCount", warnings.size());

                    // 统计各级别预警数量
                    Map<String, Integer> levelCounts = new HashMap<>();
                    for (TyphoonShipWarning warning : warnings) {
                        String level = warning.getLevel().name();
                        levelCounts.put(level, levelCounts.getOrDefault(level, 0) + 1);

                        totalWarnings++;
                        switch (warning.getLevel()) {
                            case CRITICAL:
                                criticalWarnings++;
                                break;
                            case HIGH:
                                highWarnings++;
                                break;
                            case MEDIUM:
                                mediumWarnings++;
                                break;
                        }
                    }
                    typhoonData.put("levelCounts", levelCounts);
                    typhoonWarnings.add(typhoonData);
                }
            }

            summaryData.put("totalWarnings", totalWarnings);
            summaryData.put("criticalWarnings", criticalWarnings);
            summaryData.put("highWarnings", highWarnings);
            summaryData.put("mediumWarnings", mediumWarnings);
            summaryData.put("typhoonWarnings", typhoonWarnings);
            summaryData.put("updateTime", System.currentTimeMillis());

            ValueOperations<String, String> valueOps = redisTemplate.opsForValue();
            valueOps.set(RedisParameter.TYPHOON_WARNING_SUMMARY, JSONObject.toJSONString(summaryData), 1, TimeUnit.HOURS);

        } catch (Exception e) {
            logger.error("保存台风预警汇总信息到Redis失败", e);
        }
    }

    /**
     * 从Redis获取船舶预警信息
     */
    public Map<String, Object> getShipWarningFromRedis(String shipSn) {
        try {
            ValueOperations<String, String> valueOps = redisTemplate.opsForValue();
            String warningJson = valueOps.get(RedisParameter.SHIP_WARNING + shipSn);

            if (StringUtils.hasText(warningJson)) {
                return JSONObject.parseObject(warningJson, Map.class);
            }
        } catch (Exception e) {
            logger.error("从Redis获取船舶预警信息失败: shipSn={}", shipSn, e);
        }

        return null;
    }

    /**
     * 批量获取所有船舶的预警信息
     */
    public Map<String, Map<String, Object>> getAllShipWarningsFromRedis() {
        Map<String, Map<String, Object>> allWarnings = new HashMap<>();

        try {
            ValueOperations<String, String> valueOps = redisTemplate.opsForValue();

            Set<String> warningKeys = redisTemplate.keys(RedisParameter.SHIP_WARNING + "*");

            if (warningKeys != null && !warningKeys.isEmpty()) {
                List<String> keyList = new ArrayList<>(warningKeys);
                List<String> warningJsonList = valueOps.multiGet(keyList);

                for (int i = 0; i < keyList.size(); i++) {
                    String key = keyList.get(i);
                    String warningJson = warningJsonList.get(i);

                    if (StringUtils.hasText(warningJson)) {
                        String shipSn = key.replace(RedisParameter.SHIP_WARNING, "");
                        Map<String, Object> warningData = JSONObject.parseObject(warningJson, Map.class);
                        allWarnings.put(shipSn, warningData);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("批量获取船舶预警信息失败", e);
        }

        return allWarnings;
    }
}
