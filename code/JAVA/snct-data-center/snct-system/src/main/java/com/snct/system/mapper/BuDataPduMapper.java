package com.snct.system.mapper;

import java.util.List;
import com.snct.system.domain.data.BuDataPdu;

/**
 * PDU电源分配单元数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public interface BuDataPduMapper 
{
    /**
     * 查询PDU电源分配单元数据
     * 
     * @param id PDU电源分配单元数据主键
     * @return PDU电源分配单元数据
     */
    public BuDataPdu selectBuDataPduById(Long id);

    /**
     * 查询PDU电源分配单元数据列表
     * 
     * @param buDataPdu PDU电源分配单元数据
     * @return PDU电源分配单元数据集合
     */
    public List<BuDataPdu> selectBuDataPduList(BuDataPdu buDataPdu);

    /**
     * 新增PDU电源分配单元数据
     * 
     * @param buDataPdu PDU电源分配单元数据
     * @return 结果
     */
    public int insertBuDataPdu(BuDataPdu buDataPdu);

    /**
     * 修改PDU电源分配单元数据
     * 
     * @param buDataPdu PDU电源分配单元数据
     * @return 结果
     */
    public int updateBuDataPdu(BuDataPdu buDataPdu);

    /**
     * 删除PDU电源分配单元数据
     * 
     * @param id PDU电源分配单元数据主键
     * @return 结果
     */
    public int deleteBuDataPduById(Long id);

    /**
     * 批量删除PDU电源分配单元数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBuDataPduByIds(Long[] ids);
}
