package com.snct.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * @ClassName: TableUtils
 * @Description: 表工具类
 * @author: wzewei
 * @date: 2025-09-03 17:55:07
 */
public class TableUtils {

    /**
     * MySQL表名命名规范正则表达式
     */
    private static final Pattern TABLE_NAME_PATTERN = Pattern.compile("^[a-zA-Z][a-zA-Z0-9_]*$");

    /**
     * 最大表名长度
     */
    private static final int MAX_TABLE_NAME_LENGTH = 64;

    /**
     * 验证表名是否符合MySQL命名规范
     * 
     * @param tableName 表名
     * @return true-符合规范，false-不符合规范
     */
    public static boolean isValidTableName(String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return false;
        }
        
        if (tableName.length() > MAX_TABLE_NAME_LENGTH) {
            return false;
        }
        
        return TABLE_NAME_PATTERN.matcher(tableName).matches();
    }

    /**
     * 清理字符串，使其符合MySQL表名命名规范
     * 
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    public static String cleanForTableName(String input) {
        if (StringUtils.isBlank(input)) {
            return "";
        }
        
        // 替换特殊字符为下划线
        String cleaned = input.replaceAll("[^a-zA-Z0-9_]", "_");
        
        // 确保以字母开头
        if (!cleaned.isEmpty() && !Character.isLetter(cleaned.charAt(0))) {
            cleaned = "t_" + cleaned;
        }
        
        // 移除连续的下划线
        cleaned = cleaned.replaceAll("_{2,}", "_");
        
        // 移除开头和结尾的下划线
        cleaned = cleaned.replaceAll("^_+|_+$", "");
        
        // 限制长度
        if (cleaned.length() > MAX_TABLE_NAME_LENGTH) {
            cleaned = cleaned.substring(0, MAX_TABLE_NAME_LENGTH);
        }
        
        return cleaned;
    }

    /**
     * 生成设备数据表名
     * 
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @return 表名
     */
    public static String generateDeviceTableName(String sn, String deviceCode, String deviceType) {
        String cleanSn = cleanForTableName(sn);
        String cleanDeviceCode = cleanForTableName(deviceCode);
        String cleanDeviceType = cleanForTableName(deviceType);

        String tableName = String.format("%s_%s_%s", cleanSn, cleanDeviceType, cleanDeviceCode);
        
        // 确保表名不超过最大长度
        if (tableName.length() > MAX_TABLE_NAME_LENGTH) {
            // 如果超长，尝试缩短各部分
            int maxPartLength = (MAX_TABLE_NAME_LENGTH - 2) / 3; // 减去2个下划线的长度
            cleanSn = truncateString(cleanSn, maxPartLength);
            cleanDeviceCode = truncateString(cleanDeviceCode, maxPartLength);
            cleanDeviceType = truncateString(cleanDeviceType, maxPartLength);
            tableName = String.format("%s_%s_%s", cleanSn, cleanDeviceCode, cleanDeviceType);
        }
        
        return tableName.toLowerCase();
    }

    /**
     * 截断字符串到指定长度
     * 
     * @param str 字符串
     * @param maxLength 最大长度
     * @return 截断后的字符串
     */
    private static String truncateString(String str, int maxLength) {
        if (str == null || str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength);
    }

    /**
     * 解析表名获取设备信息
     *
     * @param tableName 表名
     * @return 设备信息数组 [sn, deviceCode, deviceType]，如果解析失败返回null
     */
    public static String[] parseDeviceTableName(String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return null;
        }

        String[] parts = tableName.split("_");
        if (parts.length >= 3) {
            String sn = parts[0];
            String deviceType = parts[1];
            String deviceCode = parts[2];

            // 如果有更多部分，将它们合并到deviceCode中
            if (parts.length > 3) {
                StringBuilder sb = new StringBuilder(deviceCode);
                for (int i = 3; i < parts.length; i++) {
                    sb.append("_").append(parts[i]);
                }
                deviceCode = sb.toString();
            }

            return new String[]{sn, deviceCode, deviceType};
        }

        return null;
    }

    /**
     * 检查是否为设备数据表
     * 
     * @param tableName 表名
     * @return true-是设备数据表，false-不是
     */
    public static boolean isDeviceDataTable(String tableName) {
        if (StringUtils.isBlank(tableName)) {
            return false;
        }
        
        // 检查表名格式是否符合设备数据表的命名规范
        String[] parts = parseDeviceTableName(tableName);
        if (parts == null) {
            return false;
        }
        
        String deviceType = parts[2];
        // 检查设备类型是否为已知类型
        return isKnownDeviceType(deviceType);
    }

    /**
     * 检查是否为已知的设备类型
     * 
     * @param deviceType 设备类型
     * @return true-是已知类型，false-不是
     */
    private static boolean isKnownDeviceType(String deviceType) {
        if (StringUtils.isBlank(deviceType)) {
            return false;
        }
        
        String lowerDeviceType = deviceType.toLowerCase();
        return lowerDeviceType.equals("gps") || 
               lowerDeviceType.equals("aws") || 
               lowerDeviceType.equals("modem") || 
               lowerDeviceType.equals("pdu") || 
               lowerDeviceType.equals("attitude") || 
               lowerDeviceType.equals("amplifier");
    }

    /**
     * 生成创建表的SQL语句模板
     * 
     * @param tableName 表名
     * @param deviceType 设备类型
     * @return SQL语句
     */
    public static String generateCreateTableSql(String tableName, String deviceType) {
        switch (deviceType.toLowerCase()) {
            case "gps":
                return generateGpsTableSql(tableName);
            case "aws":
                return generateAwsTableSql(tableName);
            case "modem":
                return generateModemTableSql(tableName);
            case "pdu":
                return generatePduTableSql(tableName);
            case "attitude":
                return generateAttitudeTableSql(tableName);
            case "amplifier":
                return generateAmplifierTableSql(tableName);
            default:
                throw new IllegalArgumentException("不支持的设备类型: " + deviceType);
        }
    }

    /**
     * 生成GPS表创建SQL
     */
    private static String generateGpsTableSql(String tableName) {
        return String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint(20) DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `utc_time` varchar(50) DEFAULT NULL COMMENT 'UTC时间'," +
            "  `latitude_hemisphere` varchar(10) DEFAULT NULL COMMENT '纬度半球'," +
            "  `longitude_hemisphere` varchar(10) DEFAULT NULL COMMENT '经度半球'," +
            "  `latitude` varchar(50) DEFAULT NULL COMMENT '纬度'," +
            "  `longitude` varchar(50) DEFAULT NULL COMMENT '经度'," +
            "  `speed` varchar(20) DEFAULT NULL COMMENT '速度'," +
            "  `course` varchar(20) DEFAULT NULL COMMENT '航向'," +
            "  `date` varchar(20) DEFAULT NULL COMMENT '日期'," +
            "  `magnetic_variation` varchar(20) DEFAULT NULL COMMENT '磁偏角'," +
            "  `magnetic_variation_direction` varchar(10) DEFAULT NULL COMMENT '磁偏角方向'," +
            "  `mode_indicator` varchar(10) DEFAULT NULL COMMENT '模式指示'," +
            "  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
            "  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
            "  PRIMARY KEY (`id`)," +
            "  KEY `idx_initial_time` (`initial_time`)," +
            "  KEY `idx_initial_bj_time` (`initial_bj_time`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='GPS设备数据表_%s'",
            tableName, tableName
        );
    }

    /**
     * 生成AWS表创建SQL
     */
    private static String generateAwsTableSql(String tableName) {
        return String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint(20) DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `relative_wind` varchar(20) DEFAULT NULL COMMENT '相对风向'," +
            "  `relative_wind_speed` varchar(20) DEFAULT NULL COMMENT '相对风速'," +
            "  `air_temperature` varchar(20) DEFAULT NULL COMMENT '气温值'," +
            "  `humidity` varchar(20) DEFAULT NULL COMMENT '相对湿度数值'," +
            "  `point_tem` varchar(20) DEFAULT NULL COMMENT '露点温度数值'," +
            "  `atmospheric_pressure` varchar(20) DEFAULT NULL COMMENT '大气压力数值'," +
            "  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
            "  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
            "  PRIMARY KEY (`id`)," +
            "  KEY `idx_initial_time` (`initial_time`)," +
            "  KEY `idx_initial_bj_time` (`initial_bj_time`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AWS气象设备数据表_%s'",
            tableName, tableName
        );
    }

    /**
     * 生成Modem表创建SQL
     */
    private static String generateModemTableSql(String tableName) {
        return String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint(20) DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `signal` varchar(20) DEFAULT NULL COMMENT '信号强度'," +
            "  `speed` varchar(20) DEFAULT NULL COMMENT '速率'," +
            "  `send_power` varchar(20) DEFAULT NULL COMMENT '发送功率'," +
            "  `flag` varchar(20) DEFAULT NULL COMMENT '状态标志'," +
            "  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
            "  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
            "  PRIMARY KEY (`id`)," +
            "  KEY `idx_initial_time` (`initial_time`)," +
            "  KEY `idx_initial_bj_time` (`initial_bj_time`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卫星猫设备数据表_%s'",
            tableName, tableName
        );
    }

    /**
     * 生成PDU表创建SQL
     */
    private static String generatePduTableSql(String tableName) {
        return String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint(20) DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `voltage` varchar(20) DEFAULT NULL COMMENT '电压'," +
            "  `current` varchar(20) DEFAULT NULL COMMENT '电流'," +
            "  `power` varchar(20) DEFAULT NULL COMMENT '功率'," +
            "  `frequency` varchar(20) DEFAULT NULL COMMENT '频率'," +
            "  `temperature` varchar(20) DEFAULT NULL COMMENT '温度'," +
            "  `status` varchar(20) DEFAULT NULL COMMENT '状态'," +
            "  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
            "  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
            "  PRIMARY KEY (`id`)," +
            "  KEY `idx_initial_time` (`initial_time`)," +
            "  KEY `idx_initial_bj_time` (`initial_bj_time`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='PDU设备数据表_%s'",
            tableName, tableName
        );
    }

    /**
     * 生成姿态仪表创建SQL
     */
    private static String generateAttitudeTableSql(String tableName) {
        return String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint(20) DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `roll` varchar(20) DEFAULT NULL COMMENT '横滚角'," +
            "  `pitch` varchar(20) DEFAULT NULL COMMENT '俯仰角'," +
            "  `yaw` varchar(20) DEFAULT NULL COMMENT '偏航角'," +
            "  `acceleration_x` varchar(20) DEFAULT NULL COMMENT 'X轴加速度'," +
            "  `acceleration_y` varchar(20) DEFAULT NULL COMMENT 'Y轴加速度'," +
            "  `acceleration_z` varchar(20) DEFAULT NULL COMMENT 'Z轴加速度'," +
            "  `angular_velocity_x` varchar(20) DEFAULT NULL COMMENT 'X轴角速度'," +
            "  `angular_velocity_y` varchar(20) DEFAULT NULL COMMENT 'Y轴角速度'," +
            "  `angular_velocity_z` varchar(20) DEFAULT NULL COMMENT 'Z轴角速度'," +
            "  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
            "  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
            "  PRIMARY KEY (`id`)," +
            "  KEY `idx_initial_time` (`initial_time`)," +
            "  KEY `idx_initial_bj_time` (`initial_bj_time`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='姿态仪设备数据表_%s'",
            tableName, tableName
        );
    }

    /**
     * 生成功放表创建SQL
     */
    private static String generateAmplifierTableSql(String tableName) {
        return String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint(20) DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `input_power` varchar(20) DEFAULT NULL COMMENT '输入功率'," +
            "  `output_power` varchar(20) DEFAULT NULL COMMENT '输出功率'," +
            "  `gain` varchar(20) DEFAULT NULL COMMENT '增益'," +
            "  `temperature` varchar(20) DEFAULT NULL COMMENT '温度'," +
            "  `voltage` varchar(20) DEFAULT NULL COMMENT '电压'," +
            "  `current` varchar(20) DEFAULT NULL COMMENT '电流'," +
            "  `status` varchar(20) DEFAULT NULL COMMENT '状态'," +
            "  `alarm` varchar(20) DEFAULT NULL COMMENT '告警信息'," +
            "  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'," +
            "  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'," +
            "  PRIMARY KEY (`id`)," +
            "  KEY `idx_initial_time` (`initial_time`)," +
            "  KEY `idx_initial_bj_time` (`initial_bj_time`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='功放设备数据表_%s'",
            tableName, tableName
        );
    }
}
