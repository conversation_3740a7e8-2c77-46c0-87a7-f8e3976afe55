//package com.snct.system.service;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * @ClassName: DeviceDataUnifiedService
// * @Description: 设备数据统一查询服务（支持HBase和MySQL双存储）
// * @author: wzewei
// * @date: 2025-09-04 18:30:00
// */
//@Service
//public class DeviceDataUnifiedService {
//
//    private static final Logger logger = LoggerFactory.getLogger(DeviceDataUnifiedService.class);
//
//    @Autowired
//    private DeviceDataQueryService mysqlDeviceDataService;
//
//    @Autowired(required = false)
//    private DeviceDataService hbaseDeviceDataService;
//
//    /**
//     * 默认存储类型配置
//     * mysql: 使用MySQL存储
//     * hbase: 使用HBase存储
//     * auto: 自动选择（优先HBase，如果不可用则使用MySQL）
//     */
//    @Value("${device.data.storage.type:auto}")
//    private String defaultStorageType;
//
//    /**
//     * 统一分页查询设备数据
//     *
//     * @param sn 设备序列号
//     * @param deviceCode 设备编号
//     * @param deviceType 设备类型
//     * @param interval 时间间隔（分钟，用于数据抽稀）
//     * @param startTime 开始时间（时间戳）
//     * @param endTime 结束时间（时间戳）
//     * @param currentPage 当前页码
//     * @param pageSize 每页大小
//     * @param sort 排序方式
//     * @param storageType 存储类型（可选，不指定则使用默认配置）
//     * @return 分页数据结果
//     */
//    public Map<String, Object> queryDeviceDataPage(String sn, String deviceCode, String deviceType,
//                                                   Integer interval, Long startTime, Long endTime,
//                                                   Integer currentPage, Integer pageSize, String sort,
//                                                   String storageType) {
//        String actualStorageType = determineStorageType(storageType);
//
//        try {
//            if ("hbase".equalsIgnoreCase(actualStorageType)) {
//                return queryFromHBase(sn, deviceCode, deviceType, interval, startTime, endTime, currentPage, pageSize, sort);
//            } else {
//                return queryFromMySQL(sn, deviceCode, deviceType, interval, startTime, endTime, currentPage, pageSize, sort);
//            }
//        } catch (Exception e) {
//            logger.error("查询设备数据失败，存储类型: {}, 错误: {}", actualStorageType, e.getMessage(), e);
//
//            // 如果是自动模式且当前存储失败，尝试切换到另一种存储
//            if ("auto".equalsIgnoreCase(defaultStorageType)) {
//                try {
//                    if ("hbase".equalsIgnoreCase(actualStorageType)) {
//                        logger.info("HBase查询失败，尝试使用MySQL查询");
//                        return queryFromMySQL(sn, deviceCode, deviceType, interval, startTime, endTime, currentPage, pageSize, sort);
//                    } else {
//                        logger.info("MySQL查询失败，尝试使用HBase查询");
//                        return queryFromHBase(sn, deviceCode, deviceType, interval, startTime, endTime, currentPage, pageSize, sort);
//                    }
//                } catch (Exception fallbackException) {
//                    logger.error("备用存储查询也失败: {}", fallbackException.getMessage(), fallbackException);
//                }
//            }
//
//            return createEmptyResult();
//        }
//    }
//
//    /**
//     * 统一分页查询设备数据（简化版本，使用默认存储类型）
//     */
//    public Map<String, Object> queryDeviceDataPage(String sn, String deviceCode, String deviceType,
//                                                   Integer interval, Long startTime, Long endTime,
//                                                   Integer currentPage, Integer pageSize, String sort) {
//        return queryDeviceDataPage(sn, deviceCode, deviceType, interval, startTime, endTime,
//                                 currentPage, pageSize, sort, null);
//    }
//
//    /**
//     * 获取设备最新数据
//     *
//     * @param sn 设备序列号
//     * @param deviceCode 设备编号
//     * @param deviceType 设备类型
//     * @param storageType 存储类型（可选）
//     * @return 最新数据
//     */
//    public Map<String, Object> getLatestDeviceData(String sn, String deviceCode, String deviceType, String storageType) {
//        String actualStorageType = determineStorageType(storageType);
//
//        try {
//            if ("hbase".equalsIgnoreCase(actualStorageType)) {
//                return getLatestFromHBase(sn, deviceCode, deviceType);
//            } else {
//                return mysqlDeviceDataService.getLatestDeviceDataCompatible(sn, deviceCode, deviceType);
//            }
//        } catch (Exception e) {
//            logger.error("获取最新设备数据失败，存储类型: {}, 错误: {}", actualStorageType, e.getMessage(), e);
//
//            // 自动模式下尝试备用存储
//            if ("auto".equalsIgnoreCase(defaultStorageType)) {
//                try {
//                    if ("hbase".equalsIgnoreCase(actualStorageType)) {
//                        return mysqlDeviceDataService.getLatestDeviceDataCompatible(sn, deviceCode, deviceType);
//                    } else {
//                        return getLatestFromHBase(sn, deviceCode, deviceType);
//                    }
//                } catch (Exception fallbackException) {
//                    logger.error("备用存储获取最新数据也失败: {}", fallbackException.getMessage(), fallbackException);
//                }
//            }
//
//            return null;
//        }
//    }
//
//    /**
//     * 获取设备最新数据（简化版本）
//     */
//    public Map<String, Object> getLatestDeviceData(String sn, String deviceCode, String deviceType) {
//        return getLatestDeviceData(sn, deviceCode, deviceType, null);
//    }
//
//    /**
//     * 统计设备数据总数
//     *
//     * @param sn 设备序列号
//     * @param deviceCode 设备编号
//     * @param deviceType 设备类型
//     * @param interval 时间间隔（分钟）
//     * @param startTime 开始时间（时间戳）
//     * @param endTime 结束时间（时间戳）
//     * @param storageType 存储类型（可选）
//     * @return 数据总数
//     */
//    public long countDeviceData(String sn, String deviceCode, String deviceType, Integer interval,
//                               Long startTime, Long endTime, String storageType) {
//        String actualStorageType = determineStorageType(storageType);
//
//        try {
//            if ("hbase".equalsIgnoreCase(actualStorageType)) {
//                return countFromHBase(sn, deviceCode, deviceType, interval, startTime, endTime);
//            } else {
//                if (interval != null && interval > 0) {
//                    return mysqlDeviceDataService.countDeviceDataWithInterval(sn, deviceCode, deviceType, interval, startTime, endTime);
//                } else {
//                    return mysqlDeviceDataService.countDeviceData(sn, deviceCode, deviceType, startTime, endTime);
//                }
//            }
//        } catch (Exception e) {
//            logger.error("统计设备数据失败，存储类型: {}, 错误: {}", actualStorageType, e.getMessage(), e);
//            return 0;
//        }
//    }
//
//    /**
//     * 统计设备数据总数（简化版本）
//     */
//    public long countDeviceData(String sn, String deviceCode, String deviceType, Integer interval,
//                               Long startTime, Long endTime) {
//        return countDeviceData(sn, deviceCode, deviceType, interval, startTime, endTime, null);
//    }
//
//    /**
//     * 确定实际使用的存储类型
//     */
//    private String determineStorageType(String requestedType) {
//        if (requestedType != null && !requestedType.trim().isEmpty()) {
//            return requestedType.trim();
//        }
//
//        if ("auto".equalsIgnoreCase(defaultStorageType)) {
//            // 自动模式：优先使用HBase，如果不可用则使用MySQL
//            return isHBaseAvailable() ? "hbase" : "mysql";
//        }
//
//        return defaultStorageType;
//    }
//
//    /**
//     * 检查HBase是否可用
//     */
//    private boolean isHBaseAvailable() {
//        return hbaseDeviceDataService != null;
//    }
//
//    /**
//     * 从HBase查询数据
//     */
//    private Map<String, Object> queryFromHBase(String sn, String deviceCode, String deviceType,
//                                              Integer interval, Long startTime, Long endTime,
//                                              Integer currentPage, Integer pageSize, String sort) {
//        if (hbaseDeviceDataService == null) {
//            throw new RuntimeException("HBase服务不可用");
//        }
//
//        // 这里需要根据实际的HBase服务接口进行调用
//        // 由于HBaseDaoUtil的具体实现不在当前代码中，这里提供一个示例接口
//        String tableName = generateHBaseTableName(sn, deviceCode, deviceType, interval);
//        return hbaseDeviceDataService.queryDeviceDataPage(Integer.valueOf(deviceType), tableName, startTime, endTime,
//                                                         currentPage, pageSize, sort);
//    }
//
//    /**
//     * 从MySQL查询数据
//     */
//    private Map<String, Object> queryFromMySQL(String sn, String deviceCode, String deviceType,
//                                              Integer interval, Long startTime, Long endTime,
//                                              Integer currentPage, Integer pageSize, String sort) {
//        if (interval != null && interval > 0) {
//            return mysqlDeviceDataService.queryDeviceDataWithInterval(sn, deviceCode, deviceType, interval,
//                                                                     startTime, endTime, currentPage, pageSize, sort);
//        } else {
//            return mysqlDeviceDataService.queryDeviceDataPageCompatible(sn, deviceCode, deviceType,
//                                                                       startTime, endTime, currentPage, pageSize, sort);
//        }
//    }
//
//    /**
//     * 从HBase获取最新数据
//     */
//    private Map<String, Object> getLatestFromHBase(String sn, String deviceCode, String deviceType) {
//        if (hbaseDeviceDataService == null) {
//            throw new RuntimeException("HBase服务不可用");
//        }
//
//        // 这里需要根据实际的HBase服务接口进行调用
//        // 返回格式需要与MySQL兼容
//        throw new RuntimeException("HBase最新数据查询接口待实现");
//    }
//
//    /**
//     * 从HBase统计数据
//     */
//    private long countFromHBase(String sn, String deviceCode, String deviceType, Integer interval,
//                               Long startTime, Long endTime) {
//        if (hbaseDeviceDataService == null) {
//            throw new RuntimeException("HBase服务不可用");
//        }
//
//        // 这里需要根据实际的HBase服务接口进行调用
//        throw new RuntimeException("HBase数据统计接口待实现");
//    }
//
//    /**
//     * 生成HBase表名
//     */
//    private String generateHBaseTableName(String sn, String deviceCode, String deviceType, Integer interval) {
//        // 根据HBase的表名规则生成
//        if (interval == null) {
//            interval = 100; // 默认间隔
//        }
//        return String.format("%s_%s_%s_%d", sn, deviceType, deviceCode, interval);
//    }
//
//    /**
//     * 创建空结果
//     */
//    private Map<String, Object> createEmptyResult() {
//        Map<String, Object> result = new HashMap<>();
//        result.put("data", new java.util.ArrayList<>());
//        result.put("total", 0);
//        return result;
//    }
//
//    /**
//     * 获取当前配置的存储类型
//     */
//    public String getCurrentStorageType() {
//        return defaultStorageType;
//    }
//
//    /**
//     * 检查指定存储类型是否可用
//     */
//    public boolean isStorageTypeAvailable(String storageType) {
//        if ("mysql".equalsIgnoreCase(storageType)) {
//            return mysqlDeviceDataService != null;
//        } else if ("hbase".equalsIgnoreCase(storageType)) {
//            return hbaseDeviceDataService != null;
//        }
//        return false;
//    }
//}
