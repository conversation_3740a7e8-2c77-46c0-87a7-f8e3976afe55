package com.snct.netty;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.FixedRecvByteBufAllocator;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioDatagramChannel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @ClassName: UdpServer
 * @Description: UDP服务端
 * @author: wzewei
 * @date: 2025-09-03 17:52:35
 */
public class UdpServer {

    private Logger logger = LoggerFactory.getLogger(UdpServer.class);

    private int port;

    public UdpServer(int port) {
        this.port = port;
        bind();
    }

    private void bind() {
        EventLoopGroup group = new NioEventLoopGroup();
        try {
            Bootstrap bootstrap = new Bootstrap();
            bootstrap.group(group)
                    .channel(NioDatagramChannel.class)
                    .option(ChannelOption.SO_BROADCAST, true)
                    .option(ChannelOption.RCVBUF_ALLOCATOR, new FixedRecvByteBufAllocator(65535))
                    .handler(new NormalUdpClientHandler());

            ChannelFuture f = bootstrap.bind(this.port).sync();

            logger.info("UDP服务已启动，端口：{}", this.port);

            // 等待服务器 socket 关闭 。
            // 在这个例子中，这不会发生，但你可以优雅地关闭你的服务器。
            f.channel().closeFuture().sync();

        } catch (Exception e) {
            logger.error("启动Netty服务异常，异常信息：" + e.getMessage());
            e.printStackTrace();
        } finally {
            group.shutdownGracefully();
        }
    }


}