package com.snct.system.service.impl;

import java.util.List;

import com.snct.common.annotation.DataScope;
import com.snct.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuCruiseMapper;
import com.snct.system.domain.BuCruise;
import com.snct.system.service.IBuCruiseService;

/**
 * 航次Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-14
 */
@Service
public class BuCruiseServiceImpl implements IBuCruiseService 
{
    @Autowired
    private BuCruiseMapper buCruiseMapper;

    /**
     * 查询航次
     * 
     * @param cruiseId 航次主键
     * @return 航次
     */
    @Override
    public BuCruise selectBuCruiseByCruiseId(Long cruiseId)
    {
        return buCruiseMapper.selectBuCruiseByCruiseId(cruiseId);
    }

    /**
     * 查询航次列表
     * 
     * @param buCruise 航次
     * @return 航次
     */
    @DataScope(deptAlias = "d")
    @Override
    public List<BuCruise> selectBuCruiseList(BuCruise buCruise)
    {
        return buCruiseMapper.selectBuCruiseList(buCruise);
    }

    @DataScope(deptAlias = "d")
    @Override
    public BuCruise selectNewCruise(BuCruise buCruise)
    {
        return buCruiseMapper.selectNewCruise(buCruise);
    }

    /**
     * 新增航次
     * 
     * @param buCruise 航次
     * @return 结果
     */
    @Override
    public int insertBuCruise(BuCruise buCruise)
    {
        buCruise.setCreateTime(DateUtils.getNowDate());
        return buCruiseMapper.insertBuCruise(buCruise);
    }

    /**
     * 修改航次
     * 
     * @param buCruise 航次
     * @return 结果
     */
    @Override
    public int updateBuCruise(BuCruise buCruise)
    {
        buCruise.setUpdateTime(DateUtils.getNowDate());
        return buCruiseMapper.updateBuCruise(buCruise);
    }

    /**
     * 批量删除航次
     * 
     * @param cruiseIds 需要删除的航次主键
     * @return 结果
     */
    @Override
    public int deleteBuCruiseByCruiseIds(Long[] cruiseIds)
    {
        return buCruiseMapper.deleteBuCruiseByCruiseIds(cruiseIds);
    }

    /**
     * 删除航次信息
     * 
     * @param cruiseId 航次主键
     * @return 结果
     */
    @Override
    public int deleteBuCruiseByCruiseId(Long cruiseId)
    {
        return buCruiseMapper.deleteBuCruiseByCruiseId(cruiseId);
    }
}
