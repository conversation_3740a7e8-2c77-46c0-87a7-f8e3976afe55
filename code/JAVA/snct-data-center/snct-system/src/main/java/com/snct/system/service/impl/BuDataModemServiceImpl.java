package com.snct.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDataModemMapper;
import com.snct.system.domain.data.BuDataModem;
import com.snct.system.service.IBuDataModemService;

/**
 * 卫星猫数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
@Service
public class BuDataModemServiceImpl implements IBuDataModemService 
{
    @Autowired
    private BuDataModemMapper buDataModemMapper;

    /**
     * 查询卫星猫数据
     * 
     * @param id 卫星猫数据主键
     * @return 卫星猫数据
     */
    @Override
    public BuDataModem selectBuDataModemById(Long id)
    {
        return buDataModemMapper.selectBuDataModemById(id);
    }

    /**
     * 查询卫星猫数据列表
     * 
     * @param buDataModem 卫星猫数据
     * @return 卫星猫数据
     */
    @Override
    public List<BuDataModem> selectBuDataModemList(BuDataModem buDataModem)
    {
        return buDataModemMapper.selectBuDataModemList(buDataModem);
    }

    /**
     * 新增卫星猫数据
     * 
     * @param buDataModem 卫星猫数据
     * @return 结果
     */
    @Override
    public int insertBuDataModem(BuDataModem buDataModem)
    {
        return buDataModemMapper.insertBuDataModem(buDataModem);
    }

    /**
     * 修改卫星猫数据
     * 
     * @param buDataModem 卫星猫数据
     * @return 结果
     */
    @Override
    public int updateBuDataModem(BuDataModem buDataModem)
    {
        return buDataModemMapper.updateBuDataModem(buDataModem);
    }

    /**
     * 批量删除卫星猫数据
     * 
     * @param ids 需要删除的卫星猫数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataModemByIds(Long[] ids)
    {
        return buDataModemMapper.deleteBuDataModemByIds(ids);
    }

    /**
     * 删除卫星猫数据信息
     * 
     * @param id 卫星猫数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataModemById(Long id)
    {
        return buDataModemMapper.deleteBuDataModemById(id);
    }
}
