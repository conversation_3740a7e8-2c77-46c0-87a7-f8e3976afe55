//package com.snct.web.controller.business;
//
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.core.page.TableDataInfo;
//import com.snct.system.service.DeviceDataMysqlService;
//import com.snct.system.service.DynamicTableService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiParam;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * 动态表管理控制器
// *
// * <AUTHOR>
// * @date 2025-09-03
// */
//@Api(tags = "动态表管理")
//@RestController
//@RequestMapping("/business/dynamicTable")
//public class DynamicTableController extends BaseController {
//
//    @Autowired
//    private DynamicTableService dynamicTableService;
//
//    @Autowired
//    private DeviceDataMysqlService deviceDataMysqlService;
//
//    /**
//     * 获取设备数据表列表
//     */
//    @ApiOperation("获取设备数据表列表")
//    @PreAuthorize("@ss.hasPermi('business:dynamicTable:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(@ApiParam("设备类型") @RequestParam(required = false) String deviceType) {
//        List<String> tableList = dynamicTableService.getDeviceTableList(deviceType);
//        return getDataTable(tableList, tableList.size());
//    }
//
//    /**
//     * 创建设备数据表
//     */
//    @ApiOperation("创建设备数据表")
//    @PreAuthorize("@ss.hasPermi('business:dynamicTable:add')")
//    @PostMapping("/create")
//    public AjaxResult createTable(
//            @ApiParam("设备序列号") @RequestParam String sn,
//            @ApiParam("设备编号") @RequestParam String deviceCode,
//            @ApiParam("设备类型") @RequestParam String deviceType) {
//
//        try {
//            boolean result = dynamicTableService.createDeviceTable(sn, deviceCode, deviceType);
//            if (result) {
//                String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);
//                return AjaxResult.success("表创建成功: " + tableName);
//            } else {
//                return AjaxResult.error("表创建失败");
//            }
//        } catch (Exception e) {
//            return AjaxResult.error("表创建失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 检查表是否存在
//     */
//    @ApiOperation("检查表是否存在")
//    @PreAuthorize("@ss.hasPermi('business:dynamicTable:query')")
//    @GetMapping("/exists")
//    public AjaxResult tableExists(
//            @ApiParam("设备序列号") @RequestParam String sn,
//            @ApiParam("设备编号") @RequestParam String deviceCode,
//            @ApiParam("设备类型") @RequestParam String deviceType) {
//
//        try {
//            String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);
//            boolean exists = dynamicTableService.tableExists(tableName);
//            return AjaxResult.success().put("tableName", tableName).put("exists", exists);
//        } catch (Exception e) {
//            return AjaxResult.error("检查表失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 删除设备数据表
//     */
//    @ApiOperation("删除设备数据表")
//    @PreAuthorize("@ss.hasPermi('business:dynamicTable:remove')")
//    @DeleteMapping("/drop")
//    public AjaxResult dropTable(@ApiParam("表名") @RequestParam String tableName) {
//        try {
//            boolean result = dynamicTableService.dropDeviceTable(tableName);
//            if (result) {
//                return AjaxResult.success("表删除成功: " + tableName);
//            } else {
//                return AjaxResult.error("表删除失败");
//            }
//        } catch (Exception e) {
//            return AjaxResult.error("表删除失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 获取表信息
//     */
//    @ApiOperation("获取表信息")
//    @PreAuthorize("@ss.hasPermi('business:dynamicTable:query')")
//    @GetMapping("/info")
//    public AjaxResult getTableInfo(@ApiParam("表名") @RequestParam String tableName) {
//        try {
//            Map<String, Object> tableInfo = dynamicTableService.getTableInfo(tableName);
//            if (tableInfo != null) {
//                return AjaxResult.success(tableInfo);
//            } else {
//                return AjaxResult.error("表不存在: " + tableName);
//            }
//        } catch (Exception e) {
//            return AjaxResult.error("获取表信息失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 查询设备数据
//     */
//    @ApiOperation("查询设备数据")
//    @PreAuthorize("@ss.hasPermi('business:dynamicTable:query')")
//    @GetMapping("/data")
//    public TableDataInfo queryDeviceData(
//            @ApiParam("设备序列号") @RequestParam String sn,
//            @ApiParam("设备编号") @RequestParam String deviceCode,
//            @ApiParam("设备类型") @RequestParam String deviceType,
//            @ApiParam("开始时间") @RequestParam(required = false) Long startTime,
//            @ApiParam("结束时间") @RequestParam(required = false) Long endTime,
//            @ApiParam("限制条数") @RequestParam(required = false, defaultValue = "100") Integer limit) {
//
//        try {
//            List<Map<String, Object>> dataList = deviceDataMysqlService.queryDeviceData(
//                sn, deviceCode, deviceType, startTime, endTime, limit);
//            return getDataTable(dataList, dataList.size());
//        } catch (Exception e) {
//            logger.error("查询设备数据失败: {}", e.getMessage(), e);
//            return getDataTable(List.of(), 0);
//        }
//    }
//
//    /**
//     * 获取设备最新数据
//     */
//    @ApiOperation("获取设备最新数据")
//    @PreAuthorize("@ss.hasPermi('business:dynamicTable:query')")
//    @GetMapping("/latest")
//    public AjaxResult getLatestDeviceData(
//            @ApiParam("设备序列号") @RequestParam String sn,
//            @ApiParam("设备编号") @RequestParam String deviceCode,
//            @ApiParam("设备类型") @RequestParam String deviceType) {
//
//        try {
//            Map<String, Object> latestData = deviceDataMysqlService.getLatestDeviceData(sn, deviceCode, deviceType);
//            if (latestData != null) {
//                return AjaxResult.success(latestData);
//            } else {
//                return AjaxResult.error("未找到数据");
//            }
//        } catch (Exception e) {
//            return AjaxResult.error("获取最新数据失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 批量创建设备表
//     */
//    @ApiOperation("批量创建设备表")
//    @PreAuthorize("@ss.hasPermi('business:dynamicTable:add')")
//    @PostMapping("/batchCreate")
//    public AjaxResult batchCreateTables(@RequestBody List<Map<String, String>> deviceList) {
//        try {
//            int successCount = 0;
//            int failCount = 0;
//            StringBuilder errorMsg = new StringBuilder();
//
//            for (Map<String, String> device : deviceList) {
//                String sn = device.get("sn");
//                String deviceCode = device.get("deviceCode");
//                String deviceType = device.get("deviceType");
//
//                if (sn == null || deviceCode == null || deviceType == null) {
//                    failCount++;
//                    errorMsg.append("设备信息不完整: ").append(device).append("; ");
//                    continue;
//                }
//
//                boolean result = dynamicTableService.createDeviceTable(sn, deviceCode, deviceType);
//                if (result) {
//                    successCount++;
//                } else {
//                    failCount++;
//                    String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);
//                    errorMsg.append("创建表失败: ").append(tableName).append("; ");
//                }
//            }
//
//            String message = String.format("批量创建完成，成功: %d, 失败: %d", successCount, failCount);
//            if (failCount > 0) {
//                message += "，失败详情: " + errorMsg.toString();
//            }
//
//            return AjaxResult.success(message).put("successCount", successCount).put("failCount", failCount);
//        } catch (Exception e) {
//            return AjaxResult.error("批量创建失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 生成表名
//     */
//    @ApiOperation("生成表名")
//    @GetMapping("/generateTableName")
//    public AjaxResult generateTableName(
//            @ApiParam("设备序列号") @RequestParam String sn,
//            @ApiParam("设备编号") @RequestParam String deviceCode,
//            @ApiParam("设备类型") @RequestParam String deviceType) {
//
//        try {
//            String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);
//            return AjaxResult.success().put("tableName", tableName);
//        } catch (Exception e) {
//            return AjaxResult.error("生成表名失败: " + e.getMessage());
//        }
//    }
//}
