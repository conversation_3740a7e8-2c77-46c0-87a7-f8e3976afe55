# 第一步实现总结：扩展设备服务类，添加MySQL数据查询功能

## 实现概述

已成功为所有设备服务类添加了MySQL数据查询功能，实现了与现有HBase查询方法相对应的MySQL查询接口。

## 修改的文件列表

### 1. GpsService.java
**路径**: `code/JAVA/snct-data-center/snct-visual/src/main/java/com/snct/service/device/GpsService.java`

**新增方法**:
- `queryByTimeFromMysql(String sn, String deviceCode, String deviceType, Integer interval, Long startTime, Long endTime)`
- `getLatestDataFromMysql(String sn, String deviceCode, String deviceType)`
- `convertMysqlDataToGpsHbaseVo(Map<String, Object> mysqlData)` (私有方法)
- `getString(Map<String, Object> map, String key)` (私有工具方法)

### 2. AwsService.java
**路径**: `code/JAVA/snct-data-center/snct-visual/src/main/java/com/snct/service/device/AwsService.java`

**新增方法**:
- `queryByTimeFromMysql(String sn, String deviceCode, String deviceType, Integer interval, Long startTime, Long endTime)`
- `getLatestDataFromMysql(String sn, String deviceCode, String deviceType)`
- `convertMysqlDataToAwsHbaseVo(Map<String, Object> mysqlData)` (私有方法)
- `getString(Map<String, Object> map, String key)` (私有工具方法)

### 3. AttitudeService.java
**路径**: `code/JAVA/snct-data-center/snct-visual/src/main/java/com/snct/service/device/AttitudeService.java`

**新增方法**:
- `queryByTimeFromMysql(String sn, String deviceCode, String deviceType, Integer interval, Long startTime, Long endTime)`
- `getLatestDataFromMysql(String sn, String deviceCode, String deviceType)`
- `convertMysqlDataToAttitudeHbaseVo(Map<String, Object> mysqlData)` (私有方法)
- `getString(Map<String, Object> map, String key)` (私有工具方法)

### 4. PduService.java
**路径**: `code/JAVA/snct-data-center/snct-visual/src/main/java/com/snct/service/device/PduService.java`

**新增方法**:
- `queryByTimeFromMysql(String sn, String deviceCode, String deviceType, Integer interval, Long startTime, Long endTime)`
- `getLatestDataFromMysql(String sn, String deviceCode, String deviceType)`
- `convertMysqlDataToPduHbaseVo(Map<String, Object> mysqlData)` (私有方法)
- `getString(Map<String, Object> map, String key)` (私有工具方法)

### 5. ModemService.java
**路径**: `code/JAVA/snct-data-center/snct-visual/src/main/java/com/snct/service/device/ModemService.java`

**新增方法**:
- `queryByTimeFromMysql(String sn, String deviceCode, String deviceType, Integer interval, Long startTime, Long endTime)`
- `getLatestDataFromMysql(String sn, String deviceCode, String deviceType)`
- `convertMysqlDataToModemHbaseVo(Map<String, Object> mysqlData)` (私有方法)
- `getString(Map<String, Object> map, String key)` (私有工具方法)

### 6. AmplifierService.java
**路径**: `code/JAVA/snct-data-center/snct-visual/src/main/java/com/snct/service/device/AmplifierService.java`

**新增方法**:
- `queryByTimeFromMysql(String sn, String deviceCode, String deviceType, Integer interval, Long startTime, Long endTime)`
- `getLatestDataFromMysql(String sn, String deviceCode, String deviceType)`
- `convertMysqlDataToAmplifierHbaseVo(Map<String, Object> mysqlData)` (私有方法)
- `getString(Map<String, Object> map, String key)` (私有工具方法)

## 实现特点

### 1. 统一的接口设计
所有设备服务类都实现了相同的两个MySQL查询方法：
- **按时间范围查询**: `queryByTimeFromMysql()`
- **获取最新数据**: `getLatestDataFromMysql()`

### 2. 数据格式兼容性
- 复用现有的 `DeviceDataQueryService` 中的MySQL查询功能
- 将MySQL查询结果转换为对应的HbaseVo对象，确保返回格式与HBase查询一致
- **重要**: MySQL存储的是北斗数据，本身就有时间间隔，不需要抽稀处理
- **查询方式**: 直接按时间范围查询所有数据，不进行分页限制

### 3. 异常处理和日志记录
- 每个方法都包含完整的异常处理机制
- 详细的调试日志记录，便于问题排查
- 查询失败时返回空列表或null，不会抛出异常

### 4. 字段映射
每个设备类型都实现了专门的数据转换方法：

#### GPS设备字段映射
- `latitudeHemisphere` ← `latitudeHemisphere`
- `longitudeHemisphere` ← `longitudeHemisphere`
- `latitude` ← `latitude`
- `longitude` ← `longitude`

#### AWS设备字段映射
- `relativeWind` ← `relativeWind`
- `relativeWindSpeed` ← `relativeWindSpeed`
- `airTemperature` ← `airTemperature`
- `humidity` ← `humidity`
- `pressure` ← `pressure`
- 等等...

#### PDU设备字段映射
- `manage` ← `manage`
- `electric` ← `electric`
- `voltage` ← `voltage`
- 8个通道数据的完整映射
- 等等...

#### 其他设备类型
- Attitude: 姿态数据字段映射
- Modem: 卫星猫数据字段映射  
- Amplifier: 功放数据字段映射

### 5. 依赖注入
所有服务类都添加了对 `DeviceDataQueryService` 的依赖注入：
```java
@Autowired
private DeviceDataQueryService deviceDataQueryService;
```

## 使用示例

### 查询GPS设备的历史数据
```java
// 从MySQL查询GPS数据
List<GpsHbaseVo> gpsData = gpsService.queryByTimeFromMysql(
    "SN001", "032A", "gps", 5, 
    1693737600000L, 1693824000000L
);

// 获取GPS设备最新数据
GpsHbaseVo latestGps = gpsService.getLatestDataFromMysql(
    "SN001", "032A", "gps"
);
```

### 查询AWS设备的北斗数据
```java
// 北斗数据查询（interval参数保留但不影响查询逻辑）
List<AwsHbaseVo> awsData = awsService.queryByTimeFromMysql(
    "SN001", "031A", "aws", null,
    1693737600000L, 1693824000000L
);
```

## 技术要点

### 1. 北斗数据特点
- MySQL存储的是北斗传输数据，本身就有时间间隔，不需要抽稀处理
- `interval` 参数保留用于接口兼容性，但实际查询时直接按时间范围查询
- 统一使用 `queryDeviceDataPageCompatible()` 方法进行查询

### 2. 查询处理
- **无分页限制**: 直接按时间范围查询所有数据（使用 `Integer.MAX_VALUE` 作为页面大小）
- **时间排序**: 支持按时间倒序排列（desc），与HBase查询保持一致
- **数据量考虑**: 北斗数据量相对较少，可以一次性查询所有时间范围内的数据

### 3. 核心查询代码
```java
// 北斗数据查询：使用专门的时间范围查询方法，直接返回HBase兼容格式
List<Map<String, Object>> dataList = deviceDataQueryService.queryDeviceDataByTimeRange(
    sn, deviceCode, deviceType, startTime, endTime);
```

### 4. 统一查询方法
在 `DeviceDataQueryService` 中新增了专门的时间范围查询方法：
- **方法名**: `queryDeviceDataByTimeRange()`
- **功能**: 直接按时间范围查询所有数据，无分页限制
- **返回格式**: 已转换为HBase兼容格式的数据列表
- **优势**: 避免在每个设备服务中重复编写相同的查询逻辑

### 3. 数据转换
- 安全的类型转换，避免空指针异常
- 统一的字符串获取方法 `getString()`
- 保持与HBase数据格式的完全兼容

## 下一步计划

第一步已完成，接下来可以进行：

1. **第二步**: 创建数据源切换服务，实现HBase和MySQL之间的智能切换
2. **第三步**: 修改RealtimeService，集成数据源切换功能
3. **第四步**: 添加前端数据源切换控制界面
4. **第五步**: 实现WebSocket数据源切换支持

## 注意事项

1. **依赖关系**: 确保 `snct-manage` 模块在 `snct-visual` 模块的classpath中
2. **配置检查**: 验证MySQL数据库连接配置正确
3. **表结构**: 确保MySQL中存在对应的设备数据表
4. **字段映射**: 如有新的设备类型或字段，需要相应更新转换方法

## 测试建议

1. 单元测试每个设备服务的MySQL查询方法
2. 对比HBase和MySQL查询结果的一致性
3. 测试时间间隔抽稀功能的正确性
4. 验证异常情况下的处理逻辑
