package com.snct.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.snct.system.mapper.BuDataPduMapper;
import com.snct.system.domain.data.BuDataPdu;
import com.snct.system.service.IBuDataPduService;

/**
 * PDU电源分配单元数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
@Service
public class BuDataPduServiceImpl implements IBuDataPduService 
{
    @Autowired
    private BuDataPduMapper buDataPduMapper;

    /**
     * 查询PDU电源分配单元数据
     * 
     * @param id PDU电源分配单元数据主键
     * @return PDU电源分配单元数据
     */
    @Override
    public BuDataPdu selectBuDataPduById(Long id)
    {
        return buDataPduMapper.selectBuDataPduById(id);
    }

    /**
     * 查询PDU电源分配单元数据列表
     * 
     * @param buDataPdu PDU电源分配单元数据
     * @return PDU电源分配单元数据
     */
    @Override
    public List<BuDataPdu> selectBuDataPduList(BuDataPdu buDataPdu)
    {
        return buDataPduMapper.selectBuDataPduList(buDataPdu);
    }

    /**
     * 新增PDU电源分配单元数据
     * 
     * @param buDataPdu PDU电源分配单元数据
     * @return 结果
     */
    @Override
    public int insertBuDataPdu(BuDataPdu buDataPdu)
    {
        return buDataPduMapper.insertBuDataPdu(buDataPdu);
    }

    /**
     * 修改PDU电源分配单元数据
     * 
     * @param buDataPdu PDU电源分配单元数据
     * @return 结果
     */
    @Override
    public int updateBuDataPdu(BuDataPdu buDataPdu)
    {
        return buDataPduMapper.updateBuDataPdu(buDataPdu);
    }

    /**
     * 批量删除PDU电源分配单元数据
     * 
     * @param ids 需要删除的PDU电源分配单元数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataPduByIds(Long[] ids)
    {
        return buDataPduMapper.deleteBuDataPduByIds(ids);
    }

    /**
     * 删除PDU电源分配单元数据信息
     * 
     * @param id PDU电源分配单元数据主键
     * @return 结果
     */
    @Override
    public int deleteBuDataPduById(Long id)
    {
        return buDataPduMapper.deleteBuDataPduById(id);
    }
}
