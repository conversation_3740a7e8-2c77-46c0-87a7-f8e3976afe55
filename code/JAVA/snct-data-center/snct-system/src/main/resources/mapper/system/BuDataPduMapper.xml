<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataPduMapper">
    
    <resultMap type="BuDataPdu" id="BuDataPduResult">
        <result property="id"    column="id"    />
        <result property="initialTime"    column="initial_time"    />
        <result property="initialBjTime"    column="initial_bj_time"    />
        <result property="manage"    column="manage"    />
        <result property="electric"    column="electric"    />
        <result property="voltage"    column="voltage"    />
        <result property="yesPower"    column="yes_power"    />
        <result property="noPower"    column="no_power"    />
        <result property="seePower"    column="see_power"    />
        <result property="powerParam"    column="power_param"    />
        <result property="out1Electric"    column="out1_electric"    />
        <result property="out1Power"    column="out1_power"    />
        <result property="out1Status"    column="out1_status"    />
        <result property="out2Electric"    column="out2_electric"    />
        <result property="out2Power"    column="out2_power"    />
        <result property="out2Status"    column="out2_status"    />
        <result property="out3Electric"    column="out3_electric"    />
        <result property="out3Power"    column="out3_power"    />
        <result property="out3Status"    column="out3_status"    />
        <result property="out4Electric"    column="out4_electric"    />
        <result property="out4Power"    column="out4_power"    />
        <result property="out4Status"    column="out4_status"    />
        <result property="out5Electric"    column="out5_electric"    />
        <result property="out5Power"    column="out5_power"    />
        <result property="out5Status"    column="out5_status"    />
        <result property="out6Electric"    column="out6_electric"    />
        <result property="out6Power"    column="out6_power"    />
        <result property="out6Status"    column="out6_status"    />
        <result property="out7Electric"    column="out7_electric"    />
        <result property="out7Power"    column="out7_power"    />
        <result property="out7Status"    column="out7_status"    />
        <result property="out8Electric"    column="out8_electric"    />
        <result property="out8Power"    column="out8_power"    />
        <result property="out8Status"    column="out8_status"    />
    </resultMap>

    <sql id="selectBuDataPduVo">
        select id, initial_time, initial_bj_time, manage, electric, voltage, yes_power, no_power, see_power, power_param, out1_electric, out1_power, out1_status, out2_electric, out2_power, out2_status, out3_electric, out3_power, out3_status, out4_electric, out4_power, out4_status, out5_electric, out5_power, out5_status, out6_electric, out6_power, out6_status, out7_electric, out7_power, out7_status, out8_electric, out8_power, out8_status from bu_data_pdu
    </sql>

    <select id="selectBuDataPduList" parameterType="BuDataPdu" resultMap="BuDataPduResult">
        <include refid="selectBuDataPduVo"/>
        <where>  
            <if test="initialTime != null "> and initial_time = #{initialTime}</if>
            <if test="initialBjTime != null "> and initial_bj_time = #{initialBjTime}</if>
            <if test="manage != null  and manage != ''"> and manage = #{manage}</if>
            <if test="electric != null  and electric != ''"> and electric = #{electric}</if>
            <if test="voltage != null  and voltage != ''"> and voltage = #{voltage}</if>
            <if test="yesPower != null  and yesPower != ''"> and yes_power = #{yesPower}</if>
            <if test="noPower != null  and noPower != ''"> and no_power = #{noPower}</if>
            <if test="seePower != null  and seePower != ''"> and see_power = #{seePower}</if>
            <if test="powerParam != null  and powerParam != ''"> and power_param = #{powerParam}</if>
            <if test="out1Electric != null  and out1Electric != ''"> and out1_electric = #{out1Electric}</if>
            <if test="out1Power != null  and out1Power != ''"> and out1_power = #{out1Power}</if>
            <if test="out1Status != null  and out1Status != ''"> and out1_status = #{out1Status}</if>
            <if test="out2Electric != null  and out2Electric != ''"> and out2_electric = #{out2Electric}</if>
            <if test="out2Power != null  and out2Power != ''"> and out2_power = #{out2Power}</if>
            <if test="out2Status != null  and out2Status != ''"> and out2_status = #{out2Status}</if>
            <if test="out3Electric != null  and out3Electric != ''"> and out3_electric = #{out3Electric}</if>
            <if test="out3Power != null  and out3Power != ''"> and out3_power = #{out3Power}</if>
            <if test="out3Status != null  and out3Status != ''"> and out3_status = #{out3Status}</if>
            <if test="out4Electric != null  and out4Electric != ''"> and out4_electric = #{out4Electric}</if>
            <if test="out4Power != null  and out4Power != ''"> and out4_power = #{out4Power}</if>
            <if test="out4Status != null  and out4Status != ''"> and out4_status = #{out4Status}</if>
            <if test="out5Electric != null  and out5Electric != ''"> and out5_electric = #{out5Electric}</if>
            <if test="out5Power != null  and out5Power != ''"> and out5_power = #{out5Power}</if>
            <if test="out5Status != null  and out5Status != ''"> and out5_status = #{out5Status}</if>
            <if test="out6Electric != null  and out6Electric != ''"> and out6_electric = #{out6Electric}</if>
            <if test="out6Power != null  and out6Power != ''"> and out6_power = #{out6Power}</if>
            <if test="out6Status != null  and out6Status != ''"> and out6_status = #{out6Status}</if>
            <if test="out7Electric != null  and out7Electric != ''"> and out7_electric = #{out7Electric}</if>
            <if test="out7Power != null  and out7Power != ''"> and out7_power = #{out7Power}</if>
            <if test="out7Status != null  and out7Status != ''"> and out7_status = #{out7Status}</if>
            <if test="out8Electric != null  and out8Electric != ''"> and out8_electric = #{out8Electric}</if>
            <if test="out8Power != null  and out8Power != ''"> and out8_power = #{out8Power}</if>
            <if test="out8Status != null  and out8Status != ''"> and out8_status = #{out8Status}</if>
        </where>
    </select>
    
    <select id="selectBuDataPduById" parameterType="Long" resultMap="BuDataPduResult">
        <include refid="selectBuDataPduVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataPdu" parameterType="BuDataPdu" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_pdu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="manage != null">manage,</if>
            <if test="electric != null">electric,</if>
            <if test="voltage != null">voltage,</if>
            <if test="yesPower != null">yes_power,</if>
            <if test="noPower != null">no_power,</if>
            <if test="seePower != null">see_power,</if>
            <if test="powerParam != null">power_param,</if>
            <if test="out1Electric != null">out1_electric,</if>
            <if test="out1Power != null">out1_power,</if>
            <if test="out1Status != null">out1_status,</if>
            <if test="out2Electric != null">out2_electric,</if>
            <if test="out2Power != null">out2_power,</if>
            <if test="out2Status != null">out2_status,</if>
            <if test="out3Electric != null">out3_electric,</if>
            <if test="out3Power != null">out3_power,</if>
            <if test="out3Status != null">out3_status,</if>
            <if test="out4Electric != null">out4_electric,</if>
            <if test="out4Power != null">out4_power,</if>
            <if test="out4Status != null">out4_status,</if>
            <if test="out5Electric != null">out5_electric,</if>
            <if test="out5Power != null">out5_power,</if>
            <if test="out5Status != null">out5_status,</if>
            <if test="out6Electric != null">out6_electric,</if>
            <if test="out6Power != null">out6_power,</if>
            <if test="out6Status != null">out6_status,</if>
            <if test="out7Electric != null">out7_electric,</if>
            <if test="out7Power != null">out7_power,</if>
            <if test="out7Status != null">out7_status,</if>
            <if test="out8Electric != null">out8_electric,</if>
            <if test="out8Power != null">out8_power,</if>
            <if test="out8Status != null">out8_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="manage != null">#{manage},</if>
            <if test="electric != null">#{electric},</if>
            <if test="voltage != null">#{voltage},</if>
            <if test="yesPower != null">#{yesPower},</if>
            <if test="noPower != null">#{noPower},</if>
            <if test="seePower != null">#{seePower},</if>
            <if test="powerParam != null">#{powerParam},</if>
            <if test="out1Electric != null">#{out1Electric},</if>
            <if test="out1Power != null">#{out1Power},</if>
            <if test="out1Status != null">#{out1Status},</if>
            <if test="out2Electric != null">#{out2Electric},</if>
            <if test="out2Power != null">#{out2Power},</if>
            <if test="out2Status != null">#{out2Status},</if>
            <if test="out3Electric != null">#{out3Electric},</if>
            <if test="out3Power != null">#{out3Power},</if>
            <if test="out3Status != null">#{out3Status},</if>
            <if test="out4Electric != null">#{out4Electric},</if>
            <if test="out4Power != null">#{out4Power},</if>
            <if test="out4Status != null">#{out4Status},</if>
            <if test="out5Electric != null">#{out5Electric},</if>
            <if test="out5Power != null">#{out5Power},</if>
            <if test="out5Status != null">#{out5Status},</if>
            <if test="out6Electric != null">#{out6Electric},</if>
            <if test="out6Power != null">#{out6Power},</if>
            <if test="out6Status != null">#{out6Status},</if>
            <if test="out7Electric != null">#{out7Electric},</if>
            <if test="out7Power != null">#{out7Power},</if>
            <if test="out7Status != null">#{out7Status},</if>
            <if test="out8Electric != null">#{out8Electric},</if>
            <if test="out8Power != null">#{out8Power},</if>
            <if test="out8Status != null">#{out8Status},</if>
         </trim>
    </insert>

    <update id="updateBuDataPdu" parameterType="BuDataPdu">
        update bu_data_pdu
        <trim prefix="SET" suffixOverrides=",">
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="manage != null">manage = #{manage},</if>
            <if test="electric != null">electric = #{electric},</if>
            <if test="voltage != null">voltage = #{voltage},</if>
            <if test="yesPower != null">yes_power = #{yesPower},</if>
            <if test="noPower != null">no_power = #{noPower},</if>
            <if test="seePower != null">see_power = #{seePower},</if>
            <if test="powerParam != null">power_param = #{powerParam},</if>
            <if test="out1Electric != null">out1_electric = #{out1Electric},</if>
            <if test="out1Power != null">out1_power = #{out1Power},</if>
            <if test="out1Status != null">out1_status = #{out1Status},</if>
            <if test="out2Electric != null">out2_electric = #{out2Electric},</if>
            <if test="out2Power != null">out2_power = #{out2Power},</if>
            <if test="out2Status != null">out2_status = #{out2Status},</if>
            <if test="out3Electric != null">out3_electric = #{out3Electric},</if>
            <if test="out3Power != null">out3_power = #{out3Power},</if>
            <if test="out3Status != null">out3_status = #{out3Status},</if>
            <if test="out4Electric != null">out4_electric = #{out4Electric},</if>
            <if test="out4Power != null">out4_power = #{out4Power},</if>
            <if test="out4Status != null">out4_status = #{out4Status},</if>
            <if test="out5Electric != null">out5_electric = #{out5Electric},</if>
            <if test="out5Power != null">out5_power = #{out5Power},</if>
            <if test="out5Status != null">out5_status = #{out5Status},</if>
            <if test="out6Electric != null">out6_electric = #{out6Electric},</if>
            <if test="out6Power != null">out6_power = #{out6Power},</if>
            <if test="out6Status != null">out6_status = #{out6Status},</if>
            <if test="out7Electric != null">out7_electric = #{out7Electric},</if>
            <if test="out7Power != null">out7_power = #{out7Power},</if>
            <if test="out7Status != null">out7_status = #{out7Status},</if>
            <if test="out8Electric != null">out8_electric = #{out8Electric},</if>
            <if test="out8Power != null">out8_power = #{out8Power},</if>
            <if test="out8Status != null">out8_status = #{out8Status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataPduById" parameterType="Long">
        delete from bu_data_pdu where id = #{id}
    </delete>

    <delete id="deleteBuDataPduByIds" parameterType="String">
        delete from bu_data_pdu where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>