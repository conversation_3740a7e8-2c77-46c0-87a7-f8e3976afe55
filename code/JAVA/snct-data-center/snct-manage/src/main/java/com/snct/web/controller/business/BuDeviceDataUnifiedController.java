//package com.snct.web.controller.business;
//
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.core.page.TableDataInfo;
//import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
//import com.snct.system.service.DeviceDataUnifiedService;
//import com.snct.system.domain.Device;
//import com.snct.system.service.IDeviceService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiParam;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * @ClassName: BuDeviceDataUnifiedController
// * @Description: 设备数据统一查询控制类（支持HBase和MySQL双存储）
// * @author: wzewei
// * @date: 2025-09-04 19:00:00
// */
//@Api("设备数据统一查询管理")
//@RestController
//@RequestMapping("/business/data/unified")
//public class BuDeviceDataUnifiedController extends BaseController {
//
//    @Autowired
//    private IDeviceService deviceService;
//
//    @Autowired
//    private DeviceDataUnifiedService deviceDataUnifiedService;
//
//    @ApiOperation("分页查询设备数据列表（统一接口，兼容HBase和MySQL）")
//    @GetMapping("/list")
//    public TableDataInfo query(
//            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
//            @RequestParam(required = false) Long startTime,
//            @RequestParam(required = false) Long endTime,
//            @RequestParam(required = false) Integer interval,
//            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
//            @ApiParam("每页记录数") @RequestParam(defaultValue = "10") Integer pageSize,
//            @ApiParam("排序方式") @RequestParam(defaultValue = "desc") String sortOrder,
//            @ApiParam("存储类型") @RequestParam(required = false) String storageType) {
//
//        if (deviceId == null) {
//            return new TableDataInfo(new ArrayList<>(), 0);
//        }
//
//        Device device = deviceService.selectDeviceById(deviceId);
//        if (device == null) {
//            return new TableDataInfo(new ArrayList<>(), 0);
//        }
//
//        // 将设备类型从Integer转换为String
//        DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
//        if (deviceTypeEnum == null) {
//            return new TableDataInfo(new ArrayList<>(), 0);
//        }
//        String deviceType = deviceTypeEnum.getAlias().toLowerCase();
//
//        Map<String, Object> queryResult = deviceDataUnifiedService.queryDeviceDataPage(
//                device.getSn(), device.getCode(), deviceType, interval,
//                startTime, endTime, pageNum, pageSize, sortOrder, storageType);
//
//        return getDataTable((List<?>) queryResult.get("data"), Long.parseLong(queryResult.get("total").toString()));
//    }
//
//    @ApiOperation("获取设备最新数据（统一接口）")
//    @GetMapping("/latest")
//    public AjaxResult getLatestData(
//            @ApiParam(value = "设备ID", required = true) @RequestParam Long deviceId,
//            @ApiParam("存储类型") @RequestParam(required = false) String storageType) {
//
//        if (deviceId == null) {
//            return AjaxResult.error("设备ID不能为空");
//        }
//
//        try {
//            Device device = deviceService.selectDeviceById(deviceId);
//            if (device == null) {
//                return AjaxResult.error("设备不存在");
//            }
//
//            // 将设备类型从Integer转换为String
//            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
//            if (deviceTypeEnum == null) {
//                return AjaxResult.error("不支持的设备类型");
//            }
//            String deviceType = deviceTypeEnum.getAlias().toLowerCase();
//
//            Map<String, Object> latestData = deviceDataUnifiedService.getLatestDeviceData(
//                    device.getSn(), device.getCode(), deviceType, storageType);
//
//            if (latestData == null) {
//                return AjaxResult.success("暂无数据", null);
//            }
//
//            return AjaxResult.success("查询成功", latestData);
//        } catch (Exception e) {
//            return AjaxResult.error("查询失败：" + e.getMessage());
//        }
//    }
//
//    @ApiOperation("统计设备数据总数（统一接口）")
//    @GetMapping("/count")
//    public AjaxResult countData(
//            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
//            @RequestParam(required = false) Long startTime,
//            @RequestParam(required = false) Long endTime,
//            @RequestParam(required = false) Integer interval,
//            @ApiParam("存储类型") @RequestParam(required = false) String storageType) {
//
//        if (deviceId == null) {
//            return AjaxResult.error("设备ID不能为空");
//        }
//
//        try {
//            Device device = deviceService.selectDeviceById(deviceId);
//            if (device == null) {
//                return AjaxResult.error("设备不存在");
//            }
//
//            // 将设备类型从Integer转换为String
//            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
//            if (deviceTypeEnum == null) {
//                return AjaxResult.error("不支持的设备类型");
//            }
//            String deviceType = deviceTypeEnum.getAlias().toLowerCase();
//
//            long count = deviceDataUnifiedService.countDeviceData(
//                    device.getSn(), device.getCode(), deviceType, interval,
//                    startTime, endTime, storageType);
//
//            return AjaxResult.success("统计成功", count);
//        } catch (Exception e) {
//            return AjaxResult.error("统计失败：" + e.getMessage());
//        }
//    }
//
//    @ApiOperation("获取存储类型状态信息")
//    @GetMapping("/storage/status")
//    public AjaxResult getStorageStatus() {
//        try {
//            Map<String, Object> status = new HashMap<>();
//            status.put("currentStorageType", deviceDataUnifiedService.getCurrentStorageType());
//            status.put("mysqlAvailable", deviceDataUnifiedService.isStorageTypeAvailable("mysql"));
//            status.put("hbaseAvailable", deviceDataUnifiedService.isStorageTypeAvailable("hbase"));
//
//            return AjaxResult.success("查询成功", status);
//        } catch (Exception e) {
//            return AjaxResult.error("查询失败：" + e.getMessage());
//        }
//    }
//
//    @ApiOperation("检查指定存储类型是否可用")
//    @GetMapping("/storage/check")
//    public AjaxResult checkStorageAvailable(
//            @ApiParam(value = "存储类型", required = true) @RequestParam String storageType) {
//
//        try {
//            boolean available = deviceDataUnifiedService.isStorageTypeAvailable(storageType);
//            return AjaxResult.success("检查完成", available);
//        } catch (Exception e) {
//            return AjaxResult.error("检查失败：" + e.getMessage());
//        }
//    }
//
//    @ApiOperation("获取设备数据查询性能对比")
//    @GetMapping("/performance/compare")
//    public AjaxResult comparePerformance(
//            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
//            @RequestParam(required = false) Long startTime,
//            @RequestParam(required = false) Long endTime,
//            @RequestParam(required = false) Integer interval,
//            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
//            @ApiParam("每页记录数") @RequestParam(defaultValue = "10") Integer pageSize,
//            @ApiParam("排序方式") @RequestParam(defaultValue = "desc") String sortOrder) {
//
//        if (deviceId == null) {
//            return AjaxResult.error("设备ID不能为空");
//        }
//
//        try {
//            Device device = deviceService.selectDeviceById(deviceId);
//            if (device == null) {
//                return AjaxResult.error("设备不存在");
//            }
//
//            // 将设备类型从Integer转换为String
//            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
//            if (deviceTypeEnum == null) {
//                return AjaxResult.error("不支持的设备类型");
//            }
//            String deviceType = deviceTypeEnum.getAlias().toLowerCase();
//
//            Map<String, Object> performanceResult = new HashMap<>();
//
//            // 测试MySQL性能
//            long mysqlStartTime = System.currentTimeMillis();
//            Map<String, Object> mysqlResult = null;
//            try {
//                mysqlResult = deviceDataUnifiedService.queryDeviceDataPage(
//                        device.getSn(), device.getCode(), deviceType, interval,
//                        startTime, endTime, pageNum, pageSize, sortOrder, "mysql");
//                long mysqlEndTime = System.currentTimeMillis();
//                performanceResult.put("mysqlTime", mysqlEndTime - mysqlStartTime);
//                performanceResult.put("mysqlSuccess", true);
//                performanceResult.put("mysqlTotal", mysqlResult.get("total"));
//            } catch (Exception e) {
//                performanceResult.put("mysqlTime", -1);
//                performanceResult.put("mysqlSuccess", false);
//                performanceResult.put("mysqlError", e.getMessage());
//            }
//
//            // 测试HBase性能
//            long hbaseStartTime = System.currentTimeMillis();
//            Map<String, Object> hbaseResult = null;
//            try {
//                hbaseResult = deviceDataUnifiedService.queryDeviceDataPage(
//                        device.getSn(), device.getCode(), deviceType, interval,
//                        startTime, endTime, pageNum, pageSize, sortOrder, "hbase");
//                long hbaseEndTime = System.currentTimeMillis();
//                performanceResult.put("hbaseTime", hbaseEndTime - hbaseStartTime);
//                performanceResult.put("hbaseSuccess", true);
//                performanceResult.put("hbaseTotal", hbaseResult.get("total"));
//            } catch (Exception e) {
//                performanceResult.put("hbaseTime", -1);
//                performanceResult.put("hbaseSuccess", false);
//                performanceResult.put("hbaseError", e.getMessage());
//            }
//
//            return AjaxResult.success("性能对比完成", performanceResult);
//        } catch (Exception e) {
//            return AjaxResult.error("性能对比失败：" + e.getMessage());
//        }
//    }
//
//    @ApiOperation("批量查询多个设备的最新数据")
//    @GetMapping("/batch/latest")
//    public AjaxResult getBatchLatestData(
//            @ApiParam(value = "设备ID列表，逗号分隔", required = true) @RequestParam String deviceIds,
//            @ApiParam("存储类型") @RequestParam(required = false) String storageType) {
//
//        if (deviceIds == null || deviceIds.trim().isEmpty()) {
//            return AjaxResult.error("设备ID列表不能为空");
//        }
//
//        try {
//            String[] idArray = deviceIds.split(",");
//            Map<String, Object> batchResult = new HashMap<>();
//
//            for (String idStr : idArray) {
//                try {
//                    Long deviceId = Long.parseLong(idStr.trim());
//                    Device device = deviceService.selectDeviceById(deviceId);
//
//                    if (device != null) {
//                        // 将设备类型从Integer转换为String
//                        DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
//                        if (deviceTypeEnum != null) {
//                            String deviceType = deviceTypeEnum.getAlias().toLowerCase();
//                            Map<String, Object> latestData = deviceDataUnifiedService.getLatestDeviceData(
//                                    device.getSn(), device.getCode(), deviceType, storageType);
//                            batchResult.put(deviceId.toString(), latestData);
//                        } else {
//                            batchResult.put(deviceId.toString(), "不支持的设备类型");
//                        }
//                    } else {
//                        batchResult.put(deviceId.toString(), null);
//                    }
//                } catch (NumberFormatException e) {
//                    batchResult.put(idStr.trim(), "无效的设备ID");
//                } catch (Exception e) {
//                    batchResult.put(idStr.trim(), "查询失败: " + e.getMessage());
//                }
//            }
//
//            return AjaxResult.success("批量查询完成", batchResult);
//        } catch (Exception e) {
//            return AjaxResult.error("批量查询失败：" + e.getMessage());
//        }
//    }
//
//    @ApiOperation("获取设备数据统计信息")
//    @GetMapping("/statistics")
//    public AjaxResult getStatistics(
//            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
//            @RequestParam(required = false) Long startTime,
//            @RequestParam(required = false) Long endTime,
//            @ApiParam("存储类型") @RequestParam(required = false) String storageType) {
//
//        if (deviceId == null) {
//            return AjaxResult.error("设备ID不能为空");
//        }
//
//        try {
//            Device device = deviceService.selectDeviceById(deviceId);
//            if (device == null) {
//                return AjaxResult.error("设备不存在");
//            }
//
//            // 将设备类型从Integer转换为String
//            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
//            if (deviceTypeEnum == null) {
//                return AjaxResult.error("不支持的设备类型");
//            }
//            String deviceType = deviceTypeEnum.getAlias().toLowerCase();
//
//            Map<String, Object> statistics = new HashMap<>();
//
//            // 总数据量
//            long totalCount = deviceDataUnifiedService.countDeviceData(
//                    device.getSn(), device.getCode(), deviceType, null,
//                    startTime, endTime, storageType);
//            statistics.put("totalCount", totalCount);
//
//            // 5分钟间隔抽稀后数据量
//            long count5min = deviceDataUnifiedService.countDeviceData(
//                    device.getSn(), device.getCode(), deviceType, 5,
//                    startTime, endTime, storageType);
//            statistics.put("count5min", count5min);
//
//            // 15分钟间隔抽稀后数据量
//            long count15min = deviceDataUnifiedService.countDeviceData(
//                    device.getSn(), device.getCode(), deviceType, 15,
//                    startTime, endTime, storageType);
//            statistics.put("count15min", count15min);
//
//            // 60分钟间隔抽稀后数据量
//            long count60min = deviceDataUnifiedService.countDeviceData(
//                    device.getSn(), device.getCode(), deviceType, 60,
//                    startTime, endTime, storageType);
//            statistics.put("count60min", count60min);
//
//            // 计算数据压缩比
//            if (totalCount > 0) {
//                statistics.put("compression5min", String.format("%.2f%%", (double) count5min / totalCount * 100));
//                statistics.put("compression15min", String.format("%.2f%%", (double) count15min / totalCount * 100));
//                statistics.put("compression60min", String.format("%.2f%%", (double) count60min / totalCount * 100));
//            }
//
//            return AjaxResult.success("统计完成", statistics);
//        } catch (Exception e) {
//            return AjaxResult.error("统计失败：" + e.getMessage());
//        }
//    }
//}
