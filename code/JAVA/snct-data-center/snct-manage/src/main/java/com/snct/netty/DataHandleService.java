package com.snct.netty;

import com.snct.dctcore.commoncore.constants.RedisKeyConstants;
import com.snct.dctcore.commoncore.domain.transfer.TransferPackage;
import com.snct.dctcore.commoncore.utils.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: DataHandleService
 * @Description: 数据处理服务
 * @author: wzewei
 * @date: 2025-09-03 17:51:57
 */
@Service
public class DataHandleService {

    private Logger logger = LoggerFactory.getLogger(DataHandleService.class);

    @Autowired
    private RedisTemplate redisTemplate;
    @Resource
    private ValueOperations<String, Object> valueOperations;
    @Resource
    private ListOperations<String, Object> listOperations;
    @Autowired
    private SendService sendService;

    /**
     * 移除已经被pazu删除的数据编号
     */
    boolean removeNonExistData(TransferPackage transferPackage) {
        if (transferPackage.getDeviceType() != -1) {
            return false;
        }
        logger.info("移除一条无效数据--，{}", JsonUtil.obj2String(transferPackage));
        String key = RedisKeyConstants.getKey(RedisKeyConstants.LOSE_DATA_LIST, transferPackage.getSn());
        listOperations.remove(key, 1L, transferPackage.getCommandNum());
        return true;
    }

    /**
     * 接收数据
     *
     * @param transferPackage
     */
    void receiveMessage(TransferPackage transferPackage) {
        String sn = transferPackage.getSn();
        if (transferPackage.getSn().equalsIgnoreCase("0420033") || transferPackage.getSn().equalsIgnoreCase("0420032") || transferPackage.getSn().equalsIgnoreCase("4578426") || transferPackage.getSn().equalsIgnoreCase("5784264")) {
            return;
        }
        String key;

        // 完整消息记录到redis,设置过期时间为3天
        key = RedisKeyConstants.getKey(RedisKeyConstants.RECEIVE_COMPLETE_DATA, sn, transferPackage.getCommandNum());
        valueOperations.set(key, transferPackage.getTime(), 3, TimeUnit.DAYS);

        // 发送数据到kafka
        sendService.sendData2Kafka(transferPackage);

        logger.info("收到一条新数据--，{}", JsonUtil.obj2String(transferPackage));

    }
}