# 设备数据存储配置
device:
  data:
    storage:
      # 存储类型配置
      # mysql: 使用MySQL存储
      # hbase: 使用HBase存储  
      # auto: 自动选择（优先HBase，如果不可用则使用MySQL）
      type: auto
      
      # MySQL存储配置
      mysql:
        # 是否启用MySQL存储
        enabled: true
        # 数据保留天数（0表示永久保留）
        retention-days: 0
        # 分页查询最大页面大小
        max-page-size: 1000
        # 查询超时时间（秒）
        query-timeout: 30
        
      # HBase存储配置
      hbase:
        # 是否启用HBase存储
        enabled: true
        # 数据保留天数（0表示永久保留）
        retention-days: 0
        # 分页查询最大页面大小
        max-page-size: 1000
        # 查询超时时间（秒）
        query-timeout: 60
        # 默认时间间隔（分钟）
        default-interval: 100
        
      # 性能优化配置
      performance:
        # 是否启用查询缓存
        cache-enabled: true
        # 缓存过期时间（秒）
        cache-ttl: 300
        # 是否启用异步查询
        async-query: false
        # 查询线程池大小
        query-thread-pool-size: 10
        
      # 数据同步配置
      sync:
        # 是否启用双写模式（同时写入MySQL和HBase）
        dual-write: false
        # 是否启用数据同步检查
        sync-check: false
        # 同步检查间隔（分钟）
        sync-check-interval: 60

# 日志配置
logging:
  level:
    com.snct.service.com.snct.system.service.DeviceDataQueryService: DEBUG
    com.snct.service.com.snct.system.service.DeviceDataUnifiedService: DEBUG
    com.snct.web.controller.business.BuDeviceDataMysqlController: DEBUG
    com.snct.web.controller.business.BuDeviceDataUnifiedController: DEBUG
