package com.snct.web.controller.business;

import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.core.page.TableDataInfo;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.system.service.DeviceDataQueryService;
import com.snct.system.domain.Device;
import com.snct.system.service.IDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: BuDeviceDataMysqlController
 * @Description: 设备数据MySQL存储查询控制类
 * @author: wzewei
 * @date: 2025-09-04 18:00:00
 */
@Api("设备数据MySQL存储管理")
@RestController
@RequestMapping("/business/data/mysql")
public class BuDeviceDataMysqlController extends BaseController {

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private DeviceDataQueryService deviceDataQueryService;

    @ApiOperation("分页查询设备数据列表（MySQL存储）")
    @GetMapping("/test/list")
    public TableDataInfo query(
            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页记录数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("排序方式") @RequestParam(defaultValue = "desc") String sortOrder) {

        if (deviceId == null) {
            return new TableDataInfo(new ArrayList<>(), 0);
        }

        Device device = deviceService.selectDeviceById(deviceId);
        if (device == null) {
            return new TableDataInfo(new ArrayList<>(), 0);
        }

        // 将设备类型从Integer转换为String
        DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
        if (deviceTypeEnum == null) {
            return new TableDataInfo(new ArrayList<>(), 0);
        }
        String deviceType = deviceTypeEnum.getAlias().toLowerCase();

        Map<String, Object> queryResult = deviceDataQueryService.queryDeviceDataPageCompatible(
                device.getSn(), device.getCode(), deviceType,
                startTime, endTime, pageNum, pageSize, sortOrder);

        return getDataTable((List<?>) queryResult.get("data"), Long.parseLong(queryResult.get("total").toString()));
    }

    @ApiOperation("分页查询设备数据列表（MySQL存储，支持时间间隔抽稀）")
    @GetMapping("/list/interval")
    public TableDataInfo queryWithInterval(
            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @RequestParam(required = false) Integer interval,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页记录数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("排序方式") @RequestParam(defaultValue = "desc") String sortOrder) {

        if (deviceId == null) {
            return new TableDataInfo(new ArrayList<>(), 0);
        }

        Device device = deviceService.selectDeviceById(deviceId);
        if (device == null) {
            return new TableDataInfo(new ArrayList<>(), 0);
        }

        // 将设备类型从Integer转换为String
        DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
        if (deviceTypeEnum == null) {
            return new TableDataInfo(new ArrayList<>(), 0);
        }
        String deviceType = deviceTypeEnum.getAlias().toLowerCase();

        // 默认间隔为5分钟
        if (interval == null || interval <= 0) {
            interval = 5;
        }

        Map<String, Object> queryResult = deviceDataQueryService.queryDeviceDataWithInterval(
                device.getSn(), device.getCode(), deviceType, interval,
                startTime, endTime, pageNum, pageSize, sortOrder);

        return getDataTable((List<?>) queryResult.get("data"), Long.parseLong(queryResult.get("total").toString()));
    }

    @ApiOperation("获取设备最新数据（MySQL存储）")
    @GetMapping("/test/latest")
    public AjaxResult getLatestData(
            @ApiParam(value = "设备ID", required = true) @RequestParam Long deviceId) {

        if (deviceId == null) {
            return AjaxResult.error("设备ID不能为空");
        }

        try {
            Device device = deviceService.selectDeviceById(deviceId);
            if (device == null) {
                return AjaxResult.error("设备不存在");
            }

            // 将设备类型从Integer转换为String
            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
            if (deviceTypeEnum == null) {
                return AjaxResult.error("不支持的设备类型");
            }
            String deviceType = deviceTypeEnum.getAlias().toLowerCase();

            Map<String, Object> latestData = deviceDataQueryService.getLatestDeviceDataCompatible(
                    device.getSn(), device.getCode(), deviceType);

            if (latestData == null) {
                return AjaxResult.success("暂无数据", null);
            }

            return AjaxResult.success("查询成功", latestData);
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    @ApiOperation("统计设备数据总数（MySQL存储）")
    @GetMapping("/count")
    public AjaxResult countData(
            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime) {

        if (deviceId == null) {
            return AjaxResult.error("设备ID不能为空");
        }

        try {
            Device device = deviceService.selectDeviceById(deviceId);
            if (device == null) {
                return AjaxResult.error("设备不存在");
            }

            // 将设备类型从Integer转换为String
            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
            if (deviceTypeEnum == null) {
                return AjaxResult.error("不支持的设备类型");
            }
            String deviceType = deviceTypeEnum.getAlias().toLowerCase();

            long count = deviceDataQueryService.countDeviceData(
                    device.getSn(), device.getCode(), deviceType, startTime, endTime);

            return AjaxResult.success("统计成功", count);
        } catch (Exception e) {
            return AjaxResult.error("统计失败：" + e.getMessage());
        }
    }

    @ApiOperation("统计设备数据总数（MySQL存储，支持时间间隔抽稀）")
    @GetMapping("/count/interval")
    public AjaxResult countDataWithInterval(
            @ApiParam("设备ID") @RequestParam(required = false) Long deviceId,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @RequestParam(required = false) Integer interval) {

        if (deviceId == null) {
            return AjaxResult.error("设备ID不能为空");
        }

        try {
            Device device = deviceService.selectDeviceById(deviceId);
            if (device == null) {
                return AjaxResult.error("设备不存在");
            }

            // 将设备类型从Integer转换为String
            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
            if (deviceTypeEnum == null) {
                return AjaxResult.error("不支持的设备类型");
            }
            String deviceType = deviceTypeEnum.getAlias().toLowerCase();

            // 默认间隔为5分钟
            if (interval == null || interval <= 0) {
                interval = 5;
            }

            long count = deviceDataQueryService.countDeviceDataWithInterval(
                    device.getSn(), device.getCode(), deviceType, interval, startTime, endTime);

            return AjaxResult.success("统计成功", count);
        } catch (Exception e) {
            return AjaxResult.error("统计失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取设备数据时间范围（MySQL存储）")
    @GetMapping("/timerange")
    public AjaxResult getTimeRange(
            @ApiParam(value = "设备ID", required = true) @RequestParam Long deviceId) {

        if (deviceId == null) {
            return AjaxResult.error("设备ID不能为空");
        }

        try {
            Device device = deviceService.selectDeviceById(deviceId);
            if (device == null) {
                return AjaxResult.error("设备不存在");
            }

            // 将设备类型从Integer转换为String
            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
            if (deviceTypeEnum == null) {
                return AjaxResult.error("不支持的设备类型");
            }
            String deviceType = deviceTypeEnum.getAlias().toLowerCase();

            Map<String, Object> timeRange = deviceDataQueryService.getDeviceDataTimeRange(
                    device.getSn(), device.getCode(), deviceType);

            if (timeRange == null) {
                return AjaxResult.success("暂无数据", null);
            }

            return AjaxResult.success("查询成功", timeRange);
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    @ApiOperation("检查设备表是否存在（MySQL存储）")
    @GetMapping("/table/exists")
    public AjaxResult checkTableExists(
            @ApiParam(value = "设备ID", required = true) @RequestParam Long deviceId) {

        if (deviceId == null) {
            return AjaxResult.error("设备ID不能为空");
        }

        try {
            Device device = deviceService.selectDeviceById(deviceId);
            if (device == null) {
                return AjaxResult.error("设备不存在");
            }

            // 将设备类型从Integer转换为String
            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
            if (deviceTypeEnum == null) {
                return AjaxResult.error("不支持的设备类型");
            }
            String deviceType = deviceTypeEnum.getAlias().toLowerCase();

            boolean exists = deviceDataQueryService.deviceTableExists(
                    device.getSn(), device.getCode(), deviceType);

            return AjaxResult.success("检查完成", exists);
        } catch (Exception e) {
            return AjaxResult.error("检查失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取设备表信息（MySQL存储）")
    @GetMapping("/table/info")
    public AjaxResult getTableInfo(
            @ApiParam(value = "设备ID", required = true) @RequestParam Long deviceId) {

        if (deviceId == null) {
            return AjaxResult.error("设备ID不能为空");
        }

        try {
            Device device = deviceService.selectDeviceById(deviceId);
            if (device == null) {
                return AjaxResult.error("设备不存在");
            }

            // 将设备类型从Integer转换为String
            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
            if (deviceTypeEnum == null) {
                return AjaxResult.error("不支持的设备类型");
            }
            String deviceType = deviceTypeEnum.getAlias().toLowerCase();

            Map<String, Object> tableInfo = deviceDataQueryService.getDeviceTableInfo(
                    device.getSn(), device.getCode(), deviceType);

            if (tableInfo == null) {
                return AjaxResult.success("表不存在", null);
            }

            return AjaxResult.success("查询成功", tableInfo);
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }

    @ApiOperation("获取所有设备数据表列表（MySQL存储）")
    @GetMapping("/table/list")
    public AjaxResult getTableList(
            @ApiParam("设备类型") @RequestParam(required = false) String deviceType) {

        try {
            List<String> tableList = deviceDataQueryService.getDeviceTableList(deviceType);
            return AjaxResult.success("查询成功", tableList);
        } catch (Exception e) {
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
}
