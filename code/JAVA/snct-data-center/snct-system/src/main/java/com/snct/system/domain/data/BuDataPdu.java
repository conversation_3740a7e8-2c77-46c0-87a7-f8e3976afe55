package com.snct.system.domain.data;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * PDU电源分配单元数据对象 bu_data_pdu
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public class BuDataPdu extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 录入时间戳 */
    @Excel(name = "录入时间戳")
    private Long initialTime;

    /** 录入时间(北京时间) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "录入时间(北京时间)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /** 总电能 */
    @Excel(name = "总电能")
    private String manage;

    /** 电流 */
    @Excel(name = "电流")
    private String electric;

    /** 电压 */
    @Excel(name = "电压")
    private String voltage;

    /** 有功功率 */
    @Excel(name = "有功功率")
    private String yesPower;

    /** 无功功率 */
    @Excel(name = "无功功率")
    private String noPower;

    /** 视在功率 */
    @Excel(name = "视在功率")
    private String seePower;

    /** 功率因数 */
    @Excel(name = "功率因数")
    private String powerParam;

    /** 通道1电流 */
    @Excel(name = "通道1电流")
    private String out1Electric;

    /** 通道1功率 */
    @Excel(name = "通道1功率")
    private String out1Power;

    /** 通道1状态 */
    @Excel(name = "通道1状态")
    private String out1Status;

    /** 通道2电流 */
    @Excel(name = "通道2电流")
    private String out2Electric;

    /** 通道2功率 */
    @Excel(name = "通道2功率")
    private String out2Power;

    /** 通道2状态 */
    @Excel(name = "通道2状态")
    private String out2Status;

    /** 通道3电流 */
    @Excel(name = "通道3电流")
    private String out3Electric;

    /** 通道3功率 */
    @Excel(name = "通道3功率")
    private String out3Power;

    /** 通道3状态 */
    @Excel(name = "通道3状态")
    private String out3Status;

    /** 通道4电流 */
    @Excel(name = "通道4电流")
    private String out4Electric;

    /** 通道4功率 */
    @Excel(name = "通道4功率")
    private String out4Power;

    /** 通道4状态 */
    @Excel(name = "通道4状态")
    private String out4Status;

    /** 通道5电流 */
    @Excel(name = "通道5电流")
    private String out5Electric;

    /** 通道5功率 */
    @Excel(name = "通道5功率")
    private String out5Power;

    /** 通道5状态 */
    @Excel(name = "通道5状态")
    private String out5Status;

    /** 通道6电流 */
    @Excel(name = "通道6电流")
    private String out6Electric;

    /** 通道6功率 */
    @Excel(name = "通道6功率")
    private String out6Power;

    /** 通道6状态 */
    @Excel(name = "通道6状态")
    private String out6Status;

    /** 通道7电流 */
    @Excel(name = "通道7电流")
    private String out7Electric;

    /** 通道7功率 */
    @Excel(name = "通道7功率")
    private String out7Power;

    /** 通道7状态 */
    @Excel(name = "通道7状态")
    private String out7Status;

    /** 通道8电流 */
    @Excel(name = "通道8电流")
    private String out8Electric;

    /** 通道8功率 */
    @Excel(name = "通道8功率")
    private String out8Power;

    /** 通道8状态 */
    @Excel(name = "通道8状态")
    private String out8Status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setInitialTime(Long initialTime) 
    {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() 
    {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) 
    {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() 
    {
        return initialBjTime;
    }

    public void setManage(String manage) 
    {
        this.manage = manage;
    }

    public String getManage() 
    {
        return manage;
    }

    public void setElectric(String electric) 
    {
        this.electric = electric;
    }

    public String getElectric() 
    {
        return electric;
    }

    public void setVoltage(String voltage) 
    {
        this.voltage = voltage;
    }

    public String getVoltage() 
    {
        return voltage;
    }

    public void setYesPower(String yesPower) 
    {
        this.yesPower = yesPower;
    }

    public String getYesPower() 
    {
        return yesPower;
    }

    public void setNoPower(String noPower) 
    {
        this.noPower = noPower;
    }

    public String getNoPower() 
    {
        return noPower;
    }

    public void setSeePower(String seePower) 
    {
        this.seePower = seePower;
    }

    public String getSeePower() 
    {
        return seePower;
    }

    public void setPowerParam(String powerParam) 
    {
        this.powerParam = powerParam;
    }

    public String getPowerParam() 
    {
        return powerParam;
    }

    public void setOut1Electric(String out1Electric) 
    {
        this.out1Electric = out1Electric;
    }

    public String getOut1Electric() 
    {
        return out1Electric;
    }

    public void setOut1Power(String out1Power) 
    {
        this.out1Power = out1Power;
    }

    public String getOut1Power() 
    {
        return out1Power;
    }

    public void setOut1Status(String out1Status) 
    {
        this.out1Status = out1Status;
    }

    public String getOut1Status() 
    {
        return out1Status;
    }

    public void setOut2Electric(String out2Electric) 
    {
        this.out2Electric = out2Electric;
    }

    public String getOut2Electric() 
    {
        return out2Electric;
    }

    public void setOut2Power(String out2Power) 
    {
        this.out2Power = out2Power;
    }

    public String getOut2Power() 
    {
        return out2Power;
    }

    public void setOut2Status(String out2Status) 
    {
        this.out2Status = out2Status;
    }

    public String getOut2Status() 
    {
        return out2Status;
    }

    public void setOut3Electric(String out3Electric) 
    {
        this.out3Electric = out3Electric;
    }

    public String getOut3Electric() 
    {
        return out3Electric;
    }

    public void setOut3Power(String out3Power) 
    {
        this.out3Power = out3Power;
    }

    public String getOut3Power() 
    {
        return out3Power;
    }

    public void setOut3Status(String out3Status) 
    {
        this.out3Status = out3Status;
    }

    public String getOut3Status() 
    {
        return out3Status;
    }

    public void setOut4Electric(String out4Electric) 
    {
        this.out4Electric = out4Electric;
    }

    public String getOut4Electric() 
    {
        return out4Electric;
    }

    public void setOut4Power(String out4Power) 
    {
        this.out4Power = out4Power;
    }

    public String getOut4Power() 
    {
        return out4Power;
    }

    public void setOut4Status(String out4Status) 
    {
        this.out4Status = out4Status;
    }

    public String getOut4Status() 
    {
        return out4Status;
    }

    public void setOut5Electric(String out5Electric) 
    {
        this.out5Electric = out5Electric;
    }

    public String getOut5Electric() 
    {
        return out5Electric;
    }

    public void setOut5Power(String out5Power) 
    {
        this.out5Power = out5Power;
    }

    public String getOut5Power() 
    {
        return out5Power;
    }

    public void setOut5Status(String out5Status) 
    {
        this.out5Status = out5Status;
    }

    public String getOut5Status() 
    {
        return out5Status;
    }

    public void setOut6Electric(String out6Electric) 
    {
        this.out6Electric = out6Electric;
    }

    public String getOut6Electric() 
    {
        return out6Electric;
    }

    public void setOut6Power(String out6Power) 
    {
        this.out6Power = out6Power;
    }

    public String getOut6Power() 
    {
        return out6Power;
    }

    public void setOut6Status(String out6Status) 
    {
        this.out6Status = out6Status;
    }

    public String getOut6Status() 
    {
        return out6Status;
    }

    public void setOut7Electric(String out7Electric) 
    {
        this.out7Electric = out7Electric;
    }

    public String getOut7Electric() 
    {
        return out7Electric;
    }

    public void setOut7Power(String out7Power) 
    {
        this.out7Power = out7Power;
    }

    public String getOut7Power() 
    {
        return out7Power;
    }

    public void setOut7Status(String out7Status) 
    {
        this.out7Status = out7Status;
    }

    public String getOut7Status() 
    {
        return out7Status;
    }

    public void setOut8Electric(String out8Electric) 
    {
        this.out8Electric = out8Electric;
    }

    public String getOut8Electric() 
    {
        return out8Electric;
    }

    public void setOut8Power(String out8Power) 
    {
        this.out8Power = out8Power;
    }

    public String getOut8Power() 
    {
        return out8Power;
    }

    public void setOut8Status(String out8Status) 
    {
        this.out8Status = out8Status;
    }

    public String getOut8Status() 
    {
        return out8Status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("initialTime", getInitialTime())
            .append("initialBjTime", getInitialBjTime())
            .append("manage", getManage())
            .append("electric", getElectric())
            .append("voltage", getVoltage())
            .append("yesPower", getYesPower())
            .append("noPower", getNoPower())
            .append("seePower", getSeePower())
            .append("powerParam", getPowerParam())
            .append("out1Electric", getOut1Electric())
            .append("out1Power", getOut1Power())
            .append("out1Status", getOut1Status())
            .append("out2Electric", getOut2Electric())
            .append("out2Power", getOut2Power())
            .append("out2Status", getOut2Status())
            .append("out3Electric", getOut3Electric())
            .append("out3Power", getOut3Power())
            .append("out3Status", getOut3Status())
            .append("out4Electric", getOut4Electric())
            .append("out4Power", getOut4Power())
            .append("out4Status", getOut4Status())
            .append("out5Electric", getOut5Electric())
            .append("out5Power", getOut5Power())
            .append("out5Status", getOut5Status())
            .append("out6Electric", getOut6Electric())
            .append("out6Power", getOut6Power())
            .append("out6Status", getOut6Status())
            .append("out7Electric", getOut7Electric())
            .append("out7Power", getOut7Power())
            .append("out7Status", getOut7Status())
            .append("out8Electric", getOut8Electric())
            .append("out8Power", getOut8Power())
            .append("out8Status", getOut8Status())
            .toString();
    }
}
