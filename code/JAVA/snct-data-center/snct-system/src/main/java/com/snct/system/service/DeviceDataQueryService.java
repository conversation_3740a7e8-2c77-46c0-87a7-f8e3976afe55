package com.snct.system.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: DeviceDataQueryService
 * @Description: 设备数据查询服务
 * @author: wzewei
 * @date: 2025-09-03 17:54:32
 */
@Service
public class DeviceDataQueryService {

    private static final Logger logger = LoggerFactory.getLogger(DeviceDataQueryService.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DynamicTableService dynamicTableService;

    /**
     * 查询设备数据
     * 
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @param limit 限制条数
     * @param offset 偏移量
     * @return 数据列表
     */
    public List<Map<String, Object>> queryDeviceData(String sn, String deviceCode, String deviceType, 
                                                     Long startTime, Long endTime, Integer limit, Integer offset) {
        try {
            String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);
            
            if (!dynamicTableService.tableExists(tableName)) {
                logger.warn("表不存在: {}", tableName);
                return new ArrayList<>();
            }

            StringBuilder sql = new StringBuilder(String.format("SELECT * FROM `%s` WHERE 1=1", tableName));
            
            if (startTime != null) {
                sql.append(" AND initial_time >= ").append(startTime);
            }
            if (endTime != null) {
                sql.append(" AND initial_time <= ").append(endTime);
            }
            
            sql.append(" ORDER BY initial_time DESC");
            
            if (limit != null && limit > 0) {
                sql.append(" LIMIT ").append(limit);
                if (offset != null && offset > 0) {
                    sql.append(" OFFSET ").append(offset);
                }
            }

            return jdbcTemplate.queryForList(sql.toString());
        } catch (DataAccessException e) {
            logger.error("查询设备数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取设备最新数据
     * 
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @return 最新数据
     */
    public Map<String, Object> getLatestDeviceData(String sn, String deviceCode, String deviceType) {
        List<Map<String, Object>> result = queryDeviceData(sn, deviceCode, deviceType, null, null, 1, null);
        return result.isEmpty() ? null : result.get(0);
    }

    /**
     * 统计设备数据总数
     * 
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 数据总数
     */
    public long countDeviceData(String sn, String deviceCode, String deviceType, Long startTime, Long endTime) {
        try {
            String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);
            
            if (!dynamicTableService.tableExists(tableName)) {
                logger.warn("表不存在: {}", tableName);
                return 0;
            }

            StringBuilder sql = new StringBuilder(String.format("SELECT COUNT(*) FROM `%s` WHERE 1=1", tableName));
            
            if (startTime != null) {
                sql.append(" AND initial_time >= ").append(startTime);
            }
            if (endTime != null) {
                sql.append(" AND initial_time <= ").append(endTime);
            }

            Long count = jdbcTemplate.queryForObject(sql.toString(), Long.class);
            return count != null ? count : 0;
        } catch (DataAccessException e) {
            logger.error("统计设备数据失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 查询设备数据（分页）
     * 
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @param pageNum 页码（从1开始）
     * @param pageSize 每页大小
     * @return 分页数据结果
     */
    public Map<String, Object> queryDeviceDataPage(String sn, String deviceCode, String deviceType, 
                                                   Long startTime, Long endTime, Integer pageNum, Integer pageSize) {
        // 计算偏移量
        int offset = (pageNum - 1) * pageSize;
        
        // 查询数据
        List<Map<String, Object>> dataList = queryDeviceData(sn, deviceCode, deviceType, 
                                                             startTime, endTime, pageSize, offset);
        
        // 统计总数
        long total = countDeviceData(sn, deviceCode, deviceType, startTime, endTime);
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("data", dataList);
        result.put("total", total);
        result.put("pageNum", pageNum);
        result.put("pageSize", pageSize);
        result.put("pages", (total + pageSize - 1) / pageSize);
        
        return result;
    }

    /**
     * 查询设备数据（按时间范围聚合）
     * 
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @param interval 时间间隔（分钟）
     * @return 聚合数据
     */
    public List<Map<String, Object>> queryDeviceDataAggregated(String sn, String deviceCode, String deviceType, 
                                                               Long startTime, Long endTime, Integer interval) {
        try {
            String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);
            
            if (!dynamicTableService.tableExists(tableName)) {
                logger.warn("表不存在: {}", tableName);
                return new ArrayList<>();
            }

            // 构建聚合查询SQL
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT ");
            sql.append("  FROM_UNIXTIME(FLOOR(initial_time/1000/").append(interval * 60).append(")*").append(interval * 60).append(") as time_group, ");
            sql.append("  COUNT(*) as count, ");
            sql.append("  MIN(initial_time) as min_time, ");
            sql.append("  MAX(initial_time) as max_time ");
            sql.append("FROM `").append(tableName).append("` ");
            sql.append("WHERE 1=1 ");
            
            if (startTime != null) {
                sql.append(" AND initial_time >= ").append(startTime);
            }
            if (endTime != null) {
                sql.append(" AND initial_time <= ").append(endTime);
            }
            
            sql.append(" GROUP BY time_group ORDER BY time_group");

            return jdbcTemplate.queryForList(sql.toString());
        } catch (DataAccessException e) {
            logger.error("查询聚合设备数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取设备数据时间范围
     * 
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @return 时间范围信息
     */
    public Map<String, Object> getDeviceDataTimeRange(String sn, String deviceCode, String deviceType) {
        try {
            String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);
            
            if (!dynamicTableService.tableExists(tableName)) {
                logger.warn("表不存在: {}", tableName);
                return null;
            }

            String sql = String.format(
                "SELECT MIN(initial_time) as min_time, MAX(initial_time) as max_time, COUNT(*) as total_count FROM `%s`",
                tableName
            );

            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql);
            return result.isEmpty() ? null : result.get(0);
        } catch (DataAccessException e) {
            logger.error("获取设备数据时间范围失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查设备表是否存在
     * 
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @return true-存在，false-不存在
     */
    public boolean deviceTableExists(String sn, String deviceCode, String deviceType) {
        String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);
        return dynamicTableService.tableExists(tableName);
    }

    /**
     * 获取设备表信息
     * 
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @return 表信息
     */
    public Map<String, Object> getDeviceTableInfo(String sn, String deviceCode, String deviceType) {
        String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);
        return dynamicTableService.getTableInfo(tableName);
    }

    /**
     * 获取所有设备数据表列表
     *
     * @param deviceType 设备类型（可选）
     * @return 表名列表
     */
    public List<String> getDeviceTableList(String deviceType) {
        return dynamicTableService.getDeviceTableList(deviceType);
    }

    /**
     * 查询设备数据（分页，兼容HBase接口格式）
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @param currentPage 当前页码（从1开始）
     * @param pageSize 每页大小
     * @param sort 排序方式（desc/asc）
     * @return 分页数据结果（与HBase格式一致）
     */
    public Map<String, Object> queryDeviceDataPageCompatible(String sn, String deviceCode, String deviceType,
                                                            Long startTime, Long endTime, Integer currentPage,
                                                            Integer pageSize, String sort) {
        try {
            String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);

            if (!dynamicTableService.tableExists(tableName)) {
                logger.warn("表不存在: {}", tableName);
                return createEmptyPageResult(currentPage, pageSize);
            }

            // 构建查询SQL
            StringBuilder sql = new StringBuilder(String.format("SELECT * FROM `%s` WHERE 1=1", tableName));

            if (startTime != null) {
                sql.append(" AND initial_time >= ").append(startTime);
            }
            if (endTime != null) {
                sql.append(" AND initial_time <= ").append(endTime);
            }

            // 排序
            String orderBy = "DESC";
            if ("asc".equalsIgnoreCase(sort)) {
                orderBy = "ASC";
            }
            sql.append(" ORDER BY initial_time ").append(orderBy);

            // 分页
            int offset = (currentPage - 1) * pageSize;
            sql.append(" LIMIT ").append(pageSize).append(" OFFSET ").append(offset);

            // 查询数据
            List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sql.toString());

            // 统计总数
            long total = countDeviceData(sn, deviceCode, deviceType, startTime, endTime);

            // 转换为HBase兼容格式
            List<Map<String, Object>> convertedData = convertToHBaseCompatibleFormat(dataList, deviceType);

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("data", convertedData);
            result.put("total", total);

            return result;
        } catch (DataAccessException e) {
            logger.error("查询设备数据失败: {}", e.getMessage(), e);
            return createEmptyPageResult(currentPage, pageSize);
        }
    }

    /**
     * 查询设备数据（支持时间间隔抽稀，兼容HBase的interval功能）
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @param interval 时间间隔（分钟，用于数据抽稀）
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @param sort 排序方式
     * @return 分页数据结果
     */
    public Map<String, Object> queryDeviceDataWithInterval(String sn, String deviceCode, String deviceType,
                                                          Integer interval, Long startTime, Long endTime,
                                                          Integer currentPage, Integer pageSize, String sort) {
        try {
            String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);

            if (!dynamicTableService.tableExists(tableName)) {
                logger.warn("表不存在: {}", tableName);
                return createEmptyPageResult(currentPage, pageSize);
            }

            // 构建抽稀查询SQL
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT * FROM (");
            sql.append("  SELECT *, ROW_NUMBER() OVER (");
            sql.append("    PARTITION BY FLOOR(initial_time/1000/").append(interval * 60).append(") ");
            sql.append("    ORDER BY initial_time DESC");
            sql.append("  ) as rn");
            sql.append("  FROM `").append(tableName).append("` WHERE 1=1");

            if (startTime != null) {
                sql.append(" AND initial_time >= ").append(startTime);
            }
            if (endTime != null) {
                sql.append(" AND initial_time <= ").append(endTime);
            }

            sql.append(") t WHERE rn = 1");

            // 排序
            String orderBy = "DESC";
            if ("asc".equalsIgnoreCase(sort)) {
                orderBy = "ASC";
            }
            sql.append(" ORDER BY initial_time ").append(orderBy);

            // 分页
            int offset = (currentPage - 1) * pageSize;
            sql.append(" LIMIT ").append(pageSize).append(" OFFSET ").append(offset);

            // 查询数据
            List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sql.toString());

            // 统计抽稀后的总数
            long total = countDeviceDataWithInterval(sn, deviceCode, deviceType, interval, startTime, endTime);

            // 转换为HBase兼容格式
            List<Map<String, Object>> convertedData = convertToHBaseCompatibleFormat(dataList, deviceType);

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("data", convertedData);
            result.put("total", total);

            return result;
        } catch (DataAccessException e) {
            logger.error("查询设备数据（带间隔）失败: {}", e.getMessage(), e);
            return createEmptyPageResult(currentPage, pageSize);
        }
    }

    /**
     * 统计设备数据总数（支持时间间隔抽稀）
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @param interval 时间间隔（分钟）
     * @param startTime 开始时间（时间戳）
     * @param endTime 结束时间（时间戳）
     * @return 抽稀后的数据总数
     */
    public long countDeviceDataWithInterval(String sn, String deviceCode, String deviceType,
                                           Integer interval, Long startTime, Long endTime) {
        try {
            String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);

            if (!dynamicTableService.tableExists(tableName)) {
                logger.warn("表不存在: {}", tableName);
                return 0;
            }

            StringBuilder sql = new StringBuilder();
            sql.append("SELECT COUNT(*) FROM (");
            sql.append("  SELECT DISTINCT FLOOR(initial_time/1000/").append(interval * 60).append(") as time_group");
            sql.append("  FROM `").append(tableName).append("` WHERE 1=1");

            if (startTime != null) {
                sql.append(" AND initial_time >= ").append(startTime);
            }
            if (endTime != null) {
                sql.append(" AND initial_time <= ").append(endTime);
            }

            sql.append(") t");

            Long count = jdbcTemplate.queryForObject(sql.toString(), Long.class);
            return count != null ? count : 0;
        } catch (DataAccessException e) {
            logger.error("统计设备数据（带间隔）失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 将MySQL查询结果转换为与HBase兼容的格式
     *
     * @param mysqlResults MySQL查询结果
     * @param deviceType 设备类型
     * @return 转换后的结果列表
     */
    private List<Map<String, Object>> convertToHBaseCompatibleFormat(List<Map<String, Object>> mysqlResults, String deviceType) {
        List<Map<String, Object>> convertedResults = new ArrayList<>();

        for (Map<String, Object> row : mysqlResults) {
            Map<String, Object> convertedRow = new HashMap<>();

            // 添加通用字段
            convertedRow.put("id", row.get("id"));
            convertedRow.put("initialTime", row.get("initial_time"));
            convertedRow.put("initialBjTime", row.get("initial_bj_time"));
            convertedRow.put("utcTime", row.get("utc_time"));

            // 根据设备类型添加特定字段
            switch (deviceType.toLowerCase()) {
                case "gps":
                    convertedRow.put("latitudeHemisphere", row.get("latitude_hemisphere"));
                    convertedRow.put("longitudeHemisphere", row.get("longitude_hemisphere"));
                    convertedRow.put("latitude", row.get("latitude"));
                    convertedRow.put("longitude", row.get("longitude"));
                    break;
                case "aws":
                    convertedRow.put("relativeWind", row.get("relative_wind"));
                    convertedRow.put("relativeWindSpeed", row.get("relative_wind_speed"));
                    convertedRow.put("airTemperature", row.get("air_temperature"));
                    convertedRow.put("humidity", row.get("humidity"));
                    convertedRow.put("pointTem", row.get("point_tem"));
                    convertedRow.put("pressure", row.get("pressure"));
                    convertedRow.put("qfe", row.get("qfe"));
                    convertedRow.put("qnh", row.get("qnh"));
                    convertedRow.put("dp", row.get("dp"));
                    break;
                case "modem":
                    convertedRow.put("signal", row.get("signal"));
                    convertedRow.put("speed", row.get("speed"));
                    convertedRow.put("sendPower", row.get("send_power"));
                    convertedRow.put("flag", row.get("flag"));
                    break;
                case "pdu":
                    convertedRow.put("manage", row.get("manage"));
                    convertedRow.put("electric", row.get("electric"));
                    convertedRow.put("voltage", row.get("voltage"));
                    convertedRow.put("yesPower", row.get("yes_power"));
                    convertedRow.put("noPower", row.get("no_power"));
                    convertedRow.put("seePower", row.get("see_power"));
                    convertedRow.put("powerParam", row.get("power_param"));
                    // 添加8个通道的数据
                    for (int i = 1; i <= 8; i++) {
                        convertedRow.put("out" + i + "Electric", row.get("out" + i + "_electric"));
                        convertedRow.put("out" + i + "Power", row.get("out" + i + "_power"));
                        convertedRow.put("out" + i + "Status", row.get("out" + i + "_status"));
                    }
                    break;
                case "attitude":
                    convertedRow.put("lat", row.get("lat"));
                    convertedRow.put("lon", row.get("lon"));
                    convertedRow.put("rolling", row.get("rolling"));
                    convertedRow.put("pitch", row.get("pitch"));
                    convertedRow.put("height", row.get("height"));
                    convertedRow.put("heading", row.get("heading"));
                    convertedRow.put("speed", row.get("speed"));
                    convertedRow.put("distance", row.get("distance"));
                    convertedRow.put("stationName", row.get("station_name"));
                    break;
                case "amplifier":
                    convertedRow.put("decay", row.get("decay"));
                    convertedRow.put("temp", row.get("temp"));
                    convertedRow.put("outPower", row.get("out_power"));
                    convertedRow.put("status", row.get("status"));
                    break;
                default:
                    // 对于未知设备类型，直接复制所有字段
                    convertedRow.putAll(row);
                    break;
            }

            convertedResults.add(convertedRow);
        }

        return convertedResults;
    }

    /**
     * 创建空的分页结果
     *
     * @param currentPage 当前页码
     * @param pageSize 每页大小
     * @return 空的分页结果
     */
    private Map<String, Object> createEmptyPageResult(Integer currentPage, Integer pageSize) {
        Map<String, Object> result = new HashMap<>();
        result.put("data", new ArrayList<>());
        result.put("total", 0);
        return result;
    }

    /**
     * 生成兼容HBase格式的表名（支持interval参数）
     * 注意：MySQL实现中interval参数主要用于查询逻辑，表名保持原有格式
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @param interval 时间间隔（在MySQL实现中用于查询逻辑）
     * @return 表名
     */
    public String generateTableNameWithInterval(String sn, String deviceCode, String deviceType, Integer interval) {
        // MySQL实现中，表名不包含interval，interval用于查询时的数据抽稀
        return dynamicTableService.generateTableName(sn, deviceCode, deviceType);
    }

    /**
     * 获取设备最新数据（兼容HBase格式）
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @return 最新数据（HBase兼容格式）
     */
    public Map<String, Object> getLatestDeviceDataCompatible(String sn, String deviceCode, String deviceType) {
        Map<String, Object> latestData = getLatestDeviceData(sn, deviceCode, deviceType);
        if (latestData == null) {
            return null;
        }

        List<Map<String, Object>> dataList = new ArrayList<>();
        dataList.add(latestData);
        List<Map<String, Object>> convertedData = convertToHBaseCompatibleFormat(dataList, deviceType);

        return convertedData.isEmpty() ? null : convertedData.get(0);
    }

    /**
     * 按时间范围查询设备数据（MySQL专用，无分页限制）
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 设备数据列表（HBase兼容格式）
     */
    public List<Map<String, Object>> queryDeviceDataByTimeRange(String sn, String deviceCode, String deviceType,
                                                               Long startTime, Long endTime) {
        try {
            String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);

            if (!dynamicTableService.tableExists(tableName)) {
                logger.warn("表不存在: {}", tableName);
                return new ArrayList<>();
            }

            // 构建查询SQL
            StringBuilder sql = new StringBuilder(String.format("SELECT * FROM `%s` WHERE 1=1", tableName));

            if (startTime != null) {
                sql.append(" AND initial_time >= ").append(startTime);
            }
            if (endTime != null) {
                sql.append(" AND initial_time <= ").append(endTime);
            }

            // 按时间倒序排列
            sql.append(" ORDER BY initial_time DESC");

            // 直接查询所有数据
            List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sql.toString());

            // 转换为HBase兼容格式
            List<Map<String, Object>> convertedData = convertToHBaseCompatibleFormat(dataList, deviceType);

            logger.debug("MySQL时间范围查询成功，返回{}条记录", convertedData.size());
            return convertedData;

        } catch (DataAccessException e) {
            logger.error("MySQL时间范围查询失败: sn={}, deviceCode={}, deviceType={}", sn, deviceCode, deviceType, e);
            return new ArrayList<>();
        }
    }
}
