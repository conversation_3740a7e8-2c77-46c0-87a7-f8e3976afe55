package com.snct.system.domain.data;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * GPS数据对象 bu_data_gps
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public class BuDataGps extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 录入时间戳 */
    @Excel(name = "录入时间戳")
    private Long initialTime;

    /** 录入时间(北京时间) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "录入时间(北京时间)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /** UTC时间 */
    @Excel(name = "UTC时间")
    private String utcTime;

    /** 纬度半球 */
    @Excel(name = "纬度半球")
    private String latitudeHemisphere;

    /** 经度半球 */
    @Excel(name = "经度半球")
    private String longitudeHemisphere;

    /** 纬度 */
    @Excel(name = "纬度")
    private String latitude;

    /** 经度 */
    @Excel(name = "经度")
    private String longitude;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setInitialTime(Long initialTime) 
    {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() 
    {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) 
    {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() 
    {
        return initialBjTime;
    }

    public void setUtcTime(String utcTime) 
    {
        this.utcTime = utcTime;
    }

    public String getUtcTime() 
    {
        return utcTime;
    }

    public void setLatitudeHemisphere(String latitudeHemisphere) 
    {
        this.latitudeHemisphere = latitudeHemisphere;
    }

    public String getLatitudeHemisphere() 
    {
        return latitudeHemisphere;
    }

    public void setLongitudeHemisphere(String longitudeHemisphere) 
    {
        this.longitudeHemisphere = longitudeHemisphere;
    }

    public String getLongitudeHemisphere() 
    {
        return longitudeHemisphere;
    }

    public void setLatitude(String latitude) 
    {
        this.latitude = latitude;
    }

    public String getLatitude() 
    {
        return latitude;
    }

    public void setLongitude(String longitude) 
    {
        this.longitude = longitude;
    }

    public String getLongitude() 
    {
        return longitude;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("initialTime", getInitialTime())
            .append("initialBjTime", getInitialBjTime())
            .append("utcTime", getUtcTime())
            .append("latitudeHemisphere", getLatitudeHemisphere())
            .append("longitudeHemisphere", getLongitudeHemisphere())
            .append("latitude", getLatitude())
            .append("longitude", getLongitude())
            .toString();
    }
}
