package com.snct.system.domain.data;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;

/**
 * 卫星猫数据对象 bu_data_modem
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public class BuDataModem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 录入时间戳 */
    @Excel(name = "录入时间戳")
    private Long initialTime;

    /** 录入时间(北京时间) */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "录入时间(北京时间)", width = 30, dateFormat = "yyyy-MM-dd")
    private Date initialBjTime;

    /** 信号强度 */
    @Excel(name = "信号强度")
    private String signal;

    /** 速率 */
    @Excel(name = "速率")
    private String speed;

    /** 发送功率 */
    @Excel(name = "发送功率")
    private String sendPower;

    /** 状态标志 */
    @Excel(name = "状态标志")
    private String flag;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setInitialTime(Long initialTime) 
    {
        this.initialTime = initialTime;
    }

    public Long getInitialTime() 
    {
        return initialTime;
    }

    public void setInitialBjTime(Date initialBjTime) 
    {
        this.initialBjTime = initialBjTime;
    }

    public Date getInitialBjTime() 
    {
        return initialBjTime;
    }

    public void setSignal(String signal) 
    {
        this.signal = signal;
    }

    public String getSignal() 
    {
        return signal;
    }

    public void setSpeed(String speed) 
    {
        this.speed = speed;
    }

    public String getSpeed() 
    {
        return speed;
    }

    public void setSendPower(String sendPower) 
    {
        this.sendPower = sendPower;
    }

    public String getSendPower() 
    {
        return sendPower;
    }

    public void setFlag(String flag) 
    {
        this.flag = flag;
    }

    public String getFlag() 
    {
        return flag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("initialTime", getInitialTime())
            .append("initialBjTime", getInitialBjTime())
            .append("signal", getSignal())
            .append("speed", getSpeed())
            .append("sendPower", getSendPower())
            .append("flag", getFlag())
            .toString();
    }
}
