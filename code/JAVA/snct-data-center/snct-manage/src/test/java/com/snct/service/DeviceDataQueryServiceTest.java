//package com.snct.service;
//
//import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * @ClassName: DeviceDataQueryServiceTest
// * @Description: 设备数据查询服务测试类
// * @author: wzewei
// * @date: 2025-09-04 20:00:00
// */
//@SpringBootTest
//public class DeviceDataQueryServiceTest {
//
//    /**
//     * 测试设备类型转换
//     */
//    @Test
//    public void testDeviceTypeConversion() {
//        // 测试各种设备类型的转换
//
//        // GPS设备类型
//        DeviceTypeEnum gpsEnum = DeviceTypeEnum.getByValue(1);
//        assertNotNull(gpsEnum);
//        assertEquals("gps", gpsEnum.getAlias().toLowerCase());
//
//        // AWS设备类型
//        DeviceTypeEnum awsEnum = DeviceTypeEnum.getByValue(2);
//        assertNotNull(awsEnum);
//        assertEquals("aws", awsEnum.getAlias().toLowerCase());
//
//        // MODEM设备类型
//        DeviceTypeEnum modemEnum = DeviceTypeEnum.getByValue(3);
//        assertNotNull(modemEnum);
//        assertEquals("modem", modemEnum.getAlias().toLowerCase());
//
//        // PDU设备类型
//        DeviceTypeEnum pduEnum = DeviceTypeEnum.getByValue(4);
//        assertNotNull(pduEnum);
//        assertEquals("pdu", pduEnum.getAlias().toLowerCase());
//
//        // ATTITUDE设备类型
//        DeviceTypeEnum attitudeEnum = DeviceTypeEnum.getByValue(5);
//        assertNotNull(attitudeEnum);
//        assertEquals("attitude", attitudeEnum.getAlias().toLowerCase());
//
//        // AMPLIFIER设备类型
//        DeviceTypeEnum amplifierEnum = DeviceTypeEnum.getByValue(6);
//        assertNotNull(amplifierEnum);
//        assertEquals("amplifier", amplifierEnum.getAlias().toLowerCase());
//
//        // 测试无效的设备类型
//        DeviceTypeEnum invalidEnum = DeviceTypeEnum.getByValue(999);
//        assertNull(invalidEnum);
//    }
//
//    /**
//     * 测试设备类型转换辅助方法
//     */
//    public static String convertDeviceType(Integer deviceType) {
//        if (deviceType == null) {
//            return null;
//        }
//
//        DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(deviceType);
//        if (deviceTypeEnum == null) {
//            return null;
//        }
//
//        return deviceTypeEnum.getAlias().toLowerCase();
//    }
//
//    @Test
//    public void testConvertDeviceTypeHelper() {
//        assertEquals("gps", convertDeviceType(1));
//        assertEquals("aws", convertDeviceType(2));
//        assertEquals("modem", convertDeviceType(3));
//        assertEquals("pdu", convertDeviceType(4));
//        assertEquals("attitude", convertDeviceType(5));
//        assertEquals("amplifier", convertDeviceType(6));
//
//        assertNull(convertDeviceType(null));
//        assertNull(convertDeviceType(999));
//    }
//}
