package com.snct.service;

import com.alibaba.fastjson.JSONObject;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.service.device.*;
import com.snct.system.domain.Device;
import com.snct.system.service.impl.DeviceServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: DataSourceSwitchService
 * @Description: 数据源切换服务 - 支持网络数据(HBase)和北斗数据(MySQL)的智能切换
 * @author: wzewei
 * @date: 2025-09-04 21:00:00
 */
@Service
public class DataSourceSwitchService {

    private static final Logger logger = LoggerFactory.getLogger(DataSourceSwitchService.class);

    @Autowired
    private DeviceServiceImpl deviceService;

    @Autowired
    private GpsService gpsService;

    @Autowired
    private AwsService awsService;

    @Autowired
    private AttitudeService attitudeService;

    @Autowired
    private PduService pduService;

    @Autowired
    private ModemService modemService;

    @Autowired
    private AmplifierService amplifierService;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 数据源类型配置
     * NETWORK: 网络传输数据 (HBase)
     * BEIDOU: 北斗传输数据 (MySQL)
     * AUTO: 自动选择 (优先网络，故障时切换北斗)
     */
    @Value("${visual.data.source.type:AUTO}")
    private String dataSourceType;

    /**
     * 网络数据超时时间（秒）
     */
    @Value("${visual.data.source.network-timeout:30}")
    private int networkTimeout;

    /**
     * 是否启用数据源故障切换
     */
    @Value("${visual.data.source.failover-enabled:true}")
    private boolean failoverEnabled;

    /**
     * 获取设备最新数据（支持数据源切换）
     * 
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @return 设备数据
     */
    public Object getLatestDataWithSwitch(String sn, String deviceCode) {
        Device device = deviceService.selectDeviceBySnAndCode(sn, deviceCode);
        if (device == null) {
            logger.warn("设备不存在: sn={}, deviceCode={}", sn, deviceCode);
            return null;
        }

        String actualDataSource = determineDataSource(sn, deviceCode);
        logger.debug("使用数据源: {} 查询设备数据: sn={}, deviceCode={}", actualDataSource, sn, deviceCode);

        try {
            if ("NETWORK".equals(actualDataSource)) {
                return getDataFromNetwork(device, sn, deviceCode);
            } else if ("BEIDOU".equals(actualDataSource)) {
                return getDataFromBeidou(device, sn, deviceCode);
            } else {
                // AUTO 模式：优先网络，失败时切换北斗
                return getDataWithAutoSwitch(device, sn, deviceCode);
            }
        } catch (Exception e) {
            logger.error("获取设备数据失败: sn={}, deviceCode={}, dataSource={}", sn, deviceCode, actualDataSource, e);
            return null;
        }
    }

    /**
     * 获取设备最新数据（按设备类型）
     * 
     * @param sn 设备序列号
     * @param deviceType 设备类型
     * @return 设备数据
     */
    public Object getLatestDataWithSwitch(String sn, Long deviceType) {
        // 根据 sn 和设备类型查询设备列表
        List<Device> devices = deviceService.selectSimpleDeviceListBySnAndType(sn, deviceType);

        if (devices == null || devices.isEmpty()) {
            logger.warn("未找到设备: sn={}, deviceType={}", sn, deviceType);
            return null;
        }

        // 按优先级排序，取优先级最高的设备（cost 值最大的）
        Device selectedDevice = devices.stream()
                .filter(device -> device.getCost() != null)
                .max(Comparator.comparing(Device::getCost))
                .orElse(devices.get(0));

        return getLatestDataWithSwitch(sn, selectedDevice.getCode());
    }

    /**
     * 从网络数据源获取数据 (HBase)
     */
    private Object getDataFromNetwork(Device device, String sn, String deviceCode) {
        Object data = null;
        
        if (DeviceTypeEnum.GPS.getValue().equals(device.getType())) {
            data = gpsService.getLatestDataFromHbase(sn, deviceCode);
        } else if (DeviceTypeEnum.AWS.getValue().equals(device.getType())) {
            data = awsService.getLatestDataFromHbase(sn, deviceCode);
        } else if (DeviceTypeEnum.ATTITUDE.getValue().equals(device.getType())) {
            data = attitudeService.getLatestDataFromHbase(sn, deviceCode);
        } else if (DeviceTypeEnum.PDU.getValue().equals(device.getType())) {
            data = pduService.getLatestDataFromHbase(sn, deviceCode);
        } else if (DeviceTypeEnum.MODEM.getValue().equals(device.getType())) {
            data = modemService.getLatestDataFromHbase(sn, deviceCode);
        } else if (DeviceTypeEnum.AMPLIFIER.getValue().equals(device.getType())) {
            data = amplifierService.getLatestDataFromHbase(sn, deviceCode);
        }

        if (data != null) {
            // 缓存网络数据
            cacheData(sn, deviceCode, data, "NETWORK");
        }

        return data;
    }

    /**
     * 从北斗数据源获取数据 (MySQL)
     */
    private Object getDataFromBeidou(Device device, String sn, String deviceCode) {
        Object data = null;
        
        // 将设备类型转换为字符串
        DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(device.getType());
        if (deviceTypeEnum == null) {
            logger.warn("不支持的设备类型: {}", device.getType());
            return null;
        }
        String deviceType = deviceTypeEnum.getAlias().toLowerCase();

        if (DeviceTypeEnum.GPS.getValue().equals(device.getType())) {
            data = gpsService.getLatestDataFromMysql(sn, deviceCode, deviceType);
        } else if (DeviceTypeEnum.AWS.getValue().equals(device.getType())) {
            data = awsService.getLatestDataFromMysql(sn, deviceCode, deviceType);
        } else if (DeviceTypeEnum.ATTITUDE.getValue().equals(device.getType())) {
            data = attitudeService.getLatestDataFromMysql(sn, deviceCode, deviceType);
        } else if (DeviceTypeEnum.PDU.getValue().equals(device.getType())) {
            data = pduService.getLatestDataFromMysql(sn, deviceCode, deviceType);
        } else if (DeviceTypeEnum.MODEM.getValue().equals(device.getType())) {
            data = modemService.getLatestDataFromMysql(sn, deviceCode, deviceType);
        } else if (DeviceTypeEnum.AMPLIFIER.getValue().equals(device.getType())) {
            data = amplifierService.getLatestDataFromMysql(sn, deviceCode, deviceType);
        }

        if (data != null) {
            // 缓存北斗数据
            cacheData(sn, deviceCode, data, "BEIDOU");
        }

        return data;
    }

    /**
     * 自动切换模式获取数据
     */
    private Object getDataWithAutoSwitch(Device device, String sn, String deviceCode) {
        Object data = null;
        
        try {
            // 优先尝试网络数据
            data = getDataFromNetwork(device, sn, deviceCode);
            
            if (data != null) {
                // 记录网络数据可用
                recordDataSourceStatus(sn, deviceCode, "NETWORK", true);
                return data;
            }
        } catch (Exception e) {
            logger.warn("网络数据获取失败，尝试北斗数据: sn={}, deviceCode={}", sn, deviceCode, e);
            recordDataSourceStatus(sn, deviceCode, "NETWORK", false);
        }

        if (failoverEnabled) {
            try {
                // 网络数据不可用，尝试北斗数据
                data = getDataFromBeidou(device, sn, deviceCode);
                
                if (data != null) {
                    recordDataSourceStatus(sn, deviceCode, "BEIDOU", true);
                    logger.info("已切换到北斗数据源: sn={}, deviceCode={}", sn, deviceCode);
                } else {
                    recordDataSourceStatus(sn, deviceCode, "BEIDOU", false);
                }
            } catch (Exception e) {
                logger.error("北斗数据获取也失败: sn={}, deviceCode={}", sn, deviceCode, e);
                recordDataSourceStatus(sn, deviceCode, "BEIDOU", false);
            }
        }

        return data;
    }

    /**
     * 确定使用的数据源
     */
    private String determineDataSource(String sn, String deviceCode) {
        if ("NETWORK".equalsIgnoreCase(dataSourceType)) {
            return "NETWORK";
        } else if ("BEIDOU".equalsIgnoreCase(dataSourceType)) {
            return "BEIDOU";
        } else {
            // AUTO 模式：检查最近的数据源状态
            String lastSuccessfulSource = getLastSuccessfulDataSource(sn, deviceCode);
            return lastSuccessfulSource != null ? lastSuccessfulSource : "AUTO";
        }
    }

    /**
     * 缓存数据
     */
    private void cacheData(String sn, String deviceCode, Object data, String dataSource) {
        try {
            ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
            String cacheKey = "DEVICE_DATA_" + dataSource + ":" + sn + ":" + deviceCode;
            valueOperations.set(cacheKey, com.alibaba.fastjson.JSONObject.toJSONString(data), 3, TimeUnit.MINUTES);
        } catch (Exception e) {
            logger.warn("缓存数据失败: sn={}, deviceCode={}, dataSource={}", sn, deviceCode, dataSource, e);
        }
    }

    /**
     * 记录数据源状态
     */
    private void recordDataSourceStatus(String sn, String deviceCode, String dataSource, boolean success) {
        try {
            ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
            String statusKey = "DATA_SOURCE_STATUS:" + sn + ":" + deviceCode + ":" + dataSource;
            String statusValue = success ? "SUCCESS:" + System.currentTimeMillis() : "FAILED:" + System.currentTimeMillis();
            valueOperations.set(statusKey, statusValue, 10, TimeUnit.MINUTES);
            
            if (success) {
                // 记录最后成功的数据源
                String lastSuccessKey = "LAST_SUCCESS_SOURCE:" + sn + ":" + deviceCode;
                valueOperations.set(lastSuccessKey, dataSource, 30, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            logger.warn("记录数据源状态失败: sn={}, deviceCode={}, dataSource={}", sn, deviceCode, dataSource, e);
        }
    }

    /**
     * 获取最后成功的数据源
     */
    private String getLastSuccessfulDataSource(String sn, String deviceCode) {
        try {
            ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
            String lastSuccessKey = "LAST_SUCCESS_SOURCE:" + sn + ":" + deviceCode;
            return valueOperations.get(lastSuccessKey);
        } catch (Exception e) {
            logger.warn("获取最后成功数据源失败: sn={}, deviceCode={}", sn, deviceCode, e);
            return null;
        }
    }

    /**
     * 获取数据源状态信息
     */
    public Map<String, Object> getDataSourceStatus(String sn, String deviceCode) {
        Map<String, Object> status = new HashMap<>();
        
        try {
            ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
            
            // 网络数据源状态
            String networkStatusKey = "DATA_SOURCE_STATUS:" + sn + ":" + deviceCode + ":NETWORK";
            String networkStatus = valueOperations.get(networkStatusKey);
            status.put("networkStatus", parseStatusValue(networkStatus));
            
            // 北斗数据源状态
            String beidouStatusKey = "DATA_SOURCE_STATUS:" + sn + ":" + deviceCode + ":BEIDOU";
            String beidouStatus = valueOperations.get(beidouStatusKey);
            status.put("beidouStatus", parseStatusValue(beidouStatus));
            
            // 当前配置的数据源类型
            status.put("configuredDataSource", dataSourceType);
            
            // 最后成功的数据源
            String lastSuccessful = getLastSuccessfulDataSource(sn, deviceCode);
            status.put("lastSuccessfulSource", lastSuccessful);
            
        } catch (Exception e) {
            logger.error("获取数据源状态失败: sn={}, deviceCode={}", sn, deviceCode, e);
        }
        
        return status;
    }

    /**
     * 解析状态值
     */
    private Map<String, Object> parseStatusValue(String statusValue) {
        Map<String, Object> result = new HashMap<>();
        
        if (statusValue == null) {
            result.put("status", "UNKNOWN");
            result.put("timestamp", null);
            return result;
        }
        
        String[] parts = statusValue.split(":");
        if (parts.length == 2) {
            result.put("status", parts[0]);
            result.put("timestamp", Long.parseLong(parts[1]));
        } else {
            result.put("status", "UNKNOWN");
            result.put("timestamp", null);
        }
        
        return result;
    }
}
