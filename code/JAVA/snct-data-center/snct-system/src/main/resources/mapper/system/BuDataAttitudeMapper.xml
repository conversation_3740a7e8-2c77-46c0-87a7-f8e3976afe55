<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataAttitudeMapper">
    
    <resultMap type="BuDataAttitude" id="BuDataAttitudeResult">
        <result property="id"    column="id"    />
        <result property="initialTime"    column="initial_time"    />
        <result property="initialBjTime"    column="initial_bj_time"    />
        <result property="utcTime"    column="utc_time"    />
        <result property="lat"    column="lat"    />
        <result property="lon"    column="lon"    />
        <result property="rolling"    column="rolling"    />
        <result property="pitch"    column="pitch"    />
        <result property="height"    column="height"    />
        <result property="heading"    column="heading"    />
        <result property="speed"    column="speed"    />
        <result property="distance"    column="distance"    />
        <result property="stationName"    column="station_name"    />
    </resultMap>

    <sql id="selectBuDataAttitudeVo">
        select id, initial_time, initial_bj_time, utc_time, lat, lon, rolling, pitch, height, heading, speed, distance, station_name from bu_data_attitude
    </sql>

    <select id="selectBuDataAttitudeList" parameterType="BuDataAttitude" resultMap="BuDataAttitudeResult">
        <include refid="selectBuDataAttitudeVo"/>
        <where>  
            <if test="initialTime != null "> and initial_time = #{initialTime}</if>
            <if test="initialBjTime != null "> and initial_bj_time = #{initialBjTime}</if>
            <if test="utcTime != null  and utcTime != ''"> and utc_time = #{utcTime}</if>
            <if test="lat != null  and lat != ''"> and lat = #{lat}</if>
            <if test="lon != null  and lon != ''"> and lon = #{lon}</if>
            <if test="rolling != null  and rolling != ''"> and rolling = #{rolling}</if>
            <if test="pitch != null  and pitch != ''"> and pitch = #{pitch}</if>
            <if test="height != null  and height != ''"> and height = #{height}</if>
            <if test="heading != null  and heading != ''"> and heading = #{heading}</if>
            <if test="speed != null  and speed != ''"> and speed = #{speed}</if>
            <if test="distance != null  and distance != ''"> and distance = #{distance}</if>
            <if test="stationName != null  and stationName != ''"> and station_name like concat('%', #{stationName}, '%')</if>
        </where>
    </select>
    
    <select id="selectBuDataAttitudeById" parameterType="Long" resultMap="BuDataAttitudeResult">
        <include refid="selectBuDataAttitudeVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataAttitude" parameterType="BuDataAttitude" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_attitude
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="utcTime != null">utc_time,</if>
            <if test="lat != null">lat,</if>
            <if test="lon != null">lon,</if>
            <if test="rolling != null">rolling,</if>
            <if test="pitch != null">pitch,</if>
            <if test="height != null">height,</if>
            <if test="heading != null">heading,</if>
            <if test="speed != null">speed,</if>
            <if test="distance != null">distance,</if>
            <if test="stationName != null">station_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="utcTime != null">#{utcTime},</if>
            <if test="lat != null">#{lat},</if>
            <if test="lon != null">#{lon},</if>
            <if test="rolling != null">#{rolling},</if>
            <if test="pitch != null">#{pitch},</if>
            <if test="height != null">#{height},</if>
            <if test="heading != null">#{heading},</if>
            <if test="speed != null">#{speed},</if>
            <if test="distance != null">#{distance},</if>
            <if test="stationName != null">#{stationName},</if>
         </trim>
    </insert>

    <update id="updateBuDataAttitude" parameterType="BuDataAttitude">
        update bu_data_attitude
        <trim prefix="SET" suffixOverrides=",">
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="utcTime != null">utc_time = #{utcTime},</if>
            <if test="lat != null">lat = #{lat},</if>
            <if test="lon != null">lon = #{lon},</if>
            <if test="rolling != null">rolling = #{rolling},</if>
            <if test="pitch != null">pitch = #{pitch},</if>
            <if test="height != null">height = #{height},</if>
            <if test="heading != null">heading = #{heading},</if>
            <if test="speed != null">speed = #{speed},</if>
            <if test="distance != null">distance = #{distance},</if>
            <if test="stationName != null">station_name = #{stationName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataAttitudeById" parameterType="Long">
        delete from bu_data_attitude where id = #{id}
    </delete>

    <delete id="deleteBuDataAttitudeByIds" parameterType="String">
        delete from bu_data_attitude where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>