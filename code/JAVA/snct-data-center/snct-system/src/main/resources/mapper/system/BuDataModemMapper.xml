<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataModemMapper">
    
    <resultMap type="BuDataModem" id="BuDataModemResult">
        <result property="id"    column="id"    />
        <result property="initialTime"    column="initial_time"    />
        <result property="initialBjTime"    column="initial_bj_time"    />
        <result property="signal"    column="signal"    />
        <result property="speed"    column="speed"    />
        <result property="sendPower"    column="send_power"    />
        <result property="flag"    column="flag"    />
    </resultMap>

    <sql id="selectBuDataModemVo">
        select id, initial_time, initial_bj_time, signal, speed, send_power, flag from bu_data_modem
    </sql>

    <select id="selectBuDataModemList" parameterType="BuDataModem" resultMap="BuDataModemResult">
        <include refid="selectBuDataModemVo"/>
        <where>  
            <if test="initialTime != null "> and initial_time = #{initialTime}</if>
            <if test="initialBjTime != null "> and initial_bj_time = #{initialBjTime}</if>
            <if test="signal != null  and signal != ''"> and signal = #{signal}</if>
            <if test="speed != null  and speed != ''"> and speed = #{speed}</if>
            <if test="sendPower != null  and sendPower != ''"> and send_power = #{sendPower}</if>
            <if test="flag != null  and flag != ''"> and flag = #{flag}</if>
        </where>
    </select>
    
    <select id="selectBuDataModemById" parameterType="Long" resultMap="BuDataModemResult">
        <include refid="selectBuDataModemVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataModem" parameterType="BuDataModem" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_modem
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="signal != null">signal,</if>
            <if test="speed != null">speed,</if>
            <if test="sendPower != null">send_power,</if>
            <if test="flag != null">flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="signal != null">#{signal},</if>
            <if test="speed != null">#{speed},</if>
            <if test="sendPower != null">#{sendPower},</if>
            <if test="flag != null">#{flag},</if>
         </trim>
    </insert>

    <update id="updateBuDataModem" parameterType="BuDataModem">
        update bu_data_modem
        <trim prefix="SET" suffixOverrides=",">
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="signal != null">signal = #{signal},</if>
            <if test="speed != null">speed = #{speed},</if>
            <if test="sendPower != null">send_power = #{sendPower},</if>
            <if test="flag != null">flag = #{flag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataModemById" parameterType="Long">
        delete from bu_data_modem where id = #{id}
    </delete>

    <delete id="deleteBuDataModemByIds" parameterType="String">
        delete from bu_data_modem where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>