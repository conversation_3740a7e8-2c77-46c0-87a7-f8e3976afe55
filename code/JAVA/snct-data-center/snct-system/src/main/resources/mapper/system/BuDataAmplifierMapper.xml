<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuDataAmplifierMapper">
    
    <resultMap type="BuDataAmplifier" id="BuDataAmplifierResult">
        <result property="id"    column="id"    />
        <result property="initialTime"    column="initial_time"    />
        <result property="initialBjTime"    column="initial_bj_time"    />
        <result property="decay"    column="decay"    />
        <result property="temp"    column="temp"    />
        <result property="outPower"    column="out_power"    />
        <result property="status"    column="status"    />
    </resultMap>

    <sql id="selectBuDataAmplifierVo">
        select id, initial_time, initial_bj_time, decay, temp, out_power, status from bu_data_amplifier
    </sql>

    <select id="selectBuDataAmplifierList" parameterType="BuDataAmplifier" resultMap="BuDataAmplifierResult">
        <include refid="selectBuDataAmplifierVo"/>
        <where>  
            <if test="initialTime != null "> and initial_time = #{initialTime}</if>
            <if test="initialBjTime != null "> and initial_bj_time = #{initialBjTime}</if>
            <if test="decay != null  and decay != ''"> and decay = #{decay}</if>
            <if test="temp != null  and temp != ''"> and temp = #{temp}</if>
            <if test="outPower != null  and outPower != ''"> and out_power = #{outPower}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectBuDataAmplifierById" parameterType="Long" resultMap="BuDataAmplifierResult">
        <include refid="selectBuDataAmplifierVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuDataAmplifier" parameterType="BuDataAmplifier" useGeneratedKeys="true" keyProperty="id">
        insert into bu_data_amplifier
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">initial_time,</if>
            <if test="initialBjTime != null">initial_bj_time,</if>
            <if test="decay != null">decay,</if>
            <if test="temp != null">temp,</if>
            <if test="outPower != null">out_power,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="initialTime != null">#{initialTime},</if>
            <if test="initialBjTime != null">#{initialBjTime},</if>
            <if test="decay != null">#{decay},</if>
            <if test="temp != null">#{temp},</if>
            <if test="outPower != null">#{outPower},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateBuDataAmplifier" parameterType="BuDataAmplifier">
        update bu_data_amplifier
        <trim prefix="SET" suffixOverrides=",">
            <if test="initialTime != null">initial_time = #{initialTime},</if>
            <if test="initialBjTime != null">initial_bj_time = #{initialBjTime},</if>
            <if test="decay != null">decay = #{decay},</if>
            <if test="temp != null">temp = #{temp},</if>
            <if test="outPower != null">out_power = #{outPower},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuDataAmplifierById" parameterType="Long">
        delete from bu_data_amplifier where id = #{id}
    </delete>

    <delete id="deleteBuDataAmplifierByIds" parameterType="String">
        delete from bu_data_amplifier where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>