package com.snct.system.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: DynamicTableService
 * @Description: 动态表管理服务
 * @author: wzewei
 * @date: 2025-09-03 17:52:17
 */
@Service
public class DynamicTableService {

    private static final Logger logger = LoggerFactory.getLogger(DynamicTableService.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 生成表名
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型（如：gps, aws, modem等）
     * @return 表名
     */
    public String generateTableName(String sn, String deviceCode, String deviceType) {
        // 清理特殊字符，确保表名符合MySQL命名规范
        String cleanSn = sn.replaceAll("[^a-zA-Z0-9_]", "_");
        String cleanDeviceCode = deviceCode.replaceAll("[^a-zA-Z0-9_]", "_");
        String cleanDeviceType = deviceType.replaceAll("[^a-zA-Z0-9_]", "_").toLowerCase();

        return String.format("%s_%s_%s", cleanSn, cleanDeviceType, cleanDeviceCode);
    }

    /**
     * 检查表是否存在
     *
     * @param tableName 表名
     * @return true-存在，false-不存在
     */
    public boolean tableExists(String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
            return count != null && count > 0;
        } catch (DataAccessException e) {
            logger.error("检查表是否存在失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 强制重新创建表（删除已存在的表并重新创建）
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @return true-创建成功，false-创建失败
     */
    public boolean forceRecreateDeviceTable(String sn, String deviceCode, String deviceType) {
        String tableName = generateTableName(sn, deviceCode, deviceType);

        try {
            // 如果表存在，先删除
            if (tableExists(tableName)) {
                String dropSql = String.format("DROP TABLE `%s`", tableName);
                jdbcTemplate.execute(dropSql);
                logger.info("已删除旧表: {}", tableName);
            }

            // 重新创建表
            return createDeviceTable(sn, deviceCode, deviceType);
        } catch (DataAccessException e) {
            logger.error("强制重新创建表失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 创建GPS设备数据表
     *
     * @param tableName 表名
     * @return true-创建成功，false-创建失败
     */
    public boolean createGpsTable(String tableName) {
        if (tableExists(tableName)) {
            logger.info("表 {} 已存在，无需创建", tableName);
            return true;
        }

        String createTableSql = String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `utc_time` varchar(50) DEFAULT '' COMMENT 'UTC时间'," +
            "  `latitude_hemisphere` varchar(20) DEFAULT '' COMMENT '纬度半球'," +
            "  `longitude_hemisphere` varchar(20) DEFAULT '' COMMENT '经度半球'," +
            "  `latitude` varchar(50) DEFAULT '' COMMENT '纬度'," +
            "  `longitude` varchar(50) DEFAULT '' COMMENT '经度'," +
            "  PRIMARY KEY (`id`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='GPS数据表_%s'",
            tableName, tableName
        );

        return executeCreateTable(tableName, createTableSql);
    }

    /**
     * 创建AWS气象设备数据表
     *
     * @param tableName 表名
     * @return true-创建成功，false-创建失败
     */
    public boolean createAwsTable(String tableName) {
        if (tableExists(tableName)) {
            logger.info("表 {} 已存在，无需创建", tableName);
            return true;
        }

        String createTableSql = String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `relative_wind` varchar(50) DEFAULT '' COMMENT '相对风向'," +
            "  `relative_wind_speed` varchar(50) DEFAULT '' COMMENT '相对风速'," +
            "  `air_temperature` varchar(50) DEFAULT '' COMMENT '气温值'," +
            "  `humidity` varchar(50) DEFAULT '' COMMENT '相对湿度数值'," +
            "  `point_tem` varchar(50) DEFAULT '' COMMENT '露点温度数值'," +
            "  `pressure` varchar(50) DEFAULT '' COMMENT '气压数值'," +
            "  `qfe` varchar(50) DEFAULT '' COMMENT 'QFE气压数值'," +
            "  `qnh` varchar(50) DEFAULT '' COMMENT 'QNH气压数值'," +
            "  `dp` varchar(50) DEFAULT '' COMMENT 'DP温度'," +
            "  `utc_time` varchar(50) DEFAULT '' COMMENT 'UTC时间'," +
            "  PRIMARY KEY (`id`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AWS气象数据表_%s'",
            tableName, tableName
        );

        return executeCreateTable(tableName, createTableSql);
    }

    /**
     * 创建卫星猫设备数据表
     *
     * @param tableName 表名
     * @return true-创建成功，false-创建失败
     */
    public boolean createModemTable(String tableName) {
        if (tableExists(tableName)) {
            logger.info("表 {} 已存在，无需创建", tableName);
            return true;
        }

        String createTableSql = String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `utc_time` varchar(50) DEFAULT '' COMMENT 'UTC时间'," +
            "  `signal` varchar(50) DEFAULT '' COMMENT '信号强度'," +
            "  `speed` varchar(50) DEFAULT '' COMMENT '速率'," +
            "  `send_power` varchar(50) DEFAULT '' COMMENT '发送功率'," +
            "  `flag` varchar(20) DEFAULT '' COMMENT '状态标志'," +
            "  PRIMARY KEY (`id`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卫星猫数据表_%s'",
            tableName, tableName
        );

        return executeCreateTable(tableName, createTableSql);
    }

    /**
     * 创建PDU设备数据表
     *
     * @param tableName 表名
     * @return true-创建成功，false-创建失败
     */
    public boolean createPduTable(String tableName) {
        if (tableExists(tableName)) {
            logger.info("表 {} 已存在，无需创建", tableName);
            return true;
        }

        String createTableSql = String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `utc_time` varchar(50) DEFAULT '' COMMENT 'UTC时间'," +
            "  `manage` varchar(50) DEFAULT '' COMMENT '总电能'," +
            "  `electric` varchar(50) DEFAULT '' COMMENT '电流'," +
            "  `voltage` varchar(50) DEFAULT '' COMMENT '电压'," +
            "  `yes_power` varchar(50) DEFAULT '' COMMENT '有功功率'," +
            "  `no_power` varchar(50) DEFAULT '' COMMENT '无功功率'," +
            "  `see_power` varchar(50) DEFAULT '' COMMENT '视在功率'," +
            "  `power_param` varchar(50) DEFAULT '' COMMENT '功率因数'," +
            "  `out1_electric` varchar(50) DEFAULT '' COMMENT '通道1电流'," +
            "  `out1_power` varchar(50) DEFAULT '' COMMENT '通道1功率'," +
            "  `out1_status` varchar(20) DEFAULT '' COMMENT '通道1状态'," +
            "  `out2_electric` varchar(50) DEFAULT '' COMMENT '通道2电流'," +
            "  `out2_power` varchar(50) DEFAULT '' COMMENT '通道2功率'," +
            "  `out2_status` varchar(20) DEFAULT '' COMMENT '通道2状态'," +
            "  `out3_electric` varchar(50) DEFAULT '' COMMENT '通道3电流'," +
            "  `out3_power` varchar(50) DEFAULT '' COMMENT '通道3功率'," +
            "  `out3_status` varchar(20) DEFAULT '' COMMENT '通道3状态'," +
            "  `out4_electric` varchar(50) DEFAULT '' COMMENT '通道4电流'," +
            "  `out4_power` varchar(50) DEFAULT '' COMMENT '通道4功率'," +
            "  `out4_status` varchar(20) DEFAULT '' COMMENT '通道4状态'," +
            "  `out5_electric` varchar(50) DEFAULT '' COMMENT '通道5电流'," +
            "  `out5_power` varchar(50) DEFAULT '' COMMENT '通道5功率'," +
            "  `out5_status` varchar(20) DEFAULT '' COMMENT '通道5状态'," +
            "  `out6_electric` varchar(50) DEFAULT '' COMMENT '通道6电流'," +
            "  `out6_power` varchar(50) DEFAULT '' COMMENT '通道6功率'," +
            "  `out6_status` varchar(20) DEFAULT '' COMMENT '通道6状态'," +
            "  `out7_electric` varchar(50) DEFAULT '' COMMENT '通道7电流'," +
            "  `out7_power` varchar(50) DEFAULT '' COMMENT '通道7功率'," +
            "  `out7_status` varchar(20) DEFAULT '' COMMENT '通道7状态'," +
            "  `out8_electric` varchar(50) DEFAULT '' COMMENT '通道8电流'," +
            "  `out8_power` varchar(50) DEFAULT '' COMMENT '通道8功率'," +
            "  `out8_status` varchar(20) DEFAULT '' COMMENT '通道8状态'," +
            "  PRIMARY KEY (`id`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='PDU电源分配单元数据表_%s'",
            tableName, tableName
        );

        return executeCreateTable(tableName, createTableSql);
    }

    /**
     * 创建姿态仪设备数据表
     *
     * @param tableName 表名
     * @return true-创建成功，false-创建失败
     */
    public boolean createAttitudeTable(String tableName) {
        if (tableExists(tableName)) {
            logger.info("表 {} 已存在，无需创建", tableName);
            return true;
        }

        String createTableSql = String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `utc_time` varchar(50) DEFAULT '' COMMENT 'UTC时间'," +
            "  `lat` varchar(50) DEFAULT '' COMMENT '纬度'," +
            "  `lon` varchar(50) DEFAULT '' COMMENT '经度'," +
            "  `rolling` varchar(50) DEFAULT '' COMMENT '横摇'," +
            "  `pitch` varchar(50) DEFAULT '' COMMENT '纵摇'," +
            "  `height` varchar(50) DEFAULT '' COMMENT '高度'," +
            "  `heading` varchar(50) DEFAULT '' COMMENT '航向'," +
            "  `speed` varchar(50) DEFAULT '' COMMENT '速度'," +
            "  `distance` varchar(50) DEFAULT '' COMMENT '距离'," +
            "  `station_name` varchar(100) DEFAULT '' COMMENT '站名'," +
            "  PRIMARY KEY (`id`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='姿态仪数据表_%s'",
            tableName, tableName
        );

        return executeCreateTable(tableName, createTableSql);
    }

    /**
     * 创建功放设备数据表
     *
     * @param tableName 表名
     * @return true-创建成功，false-创建失败
     */
    public boolean createAmplifierTable(String tableName) {
        if (tableExists(tableName)) {
            logger.info("表 {} 已存在，无需创建", tableName);
            return true;
        }

        String createTableSql = String.format(
            "CREATE TABLE `%s` (" +
            "  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID'," +
            "  `initial_time` bigint DEFAULT NULL COMMENT '录入时间戳'," +
            "  `initial_bj_time` datetime DEFAULT NULL COMMENT '录入时间(北京时间)'," +
            "  `utc_time` varchar(50) DEFAULT '' COMMENT 'UTC时间'," +
            "  `decay` varchar(50) DEFAULT '' COMMENT '衰减值'," +
            "  `temp` varchar(50) DEFAULT '' COMMENT '温度'," +
            "  `out_power` varchar(50) DEFAULT '' COMMENT '输出功率'," +
            "  `status` varchar(20) DEFAULT '' COMMENT '设备状态'," +
            "  PRIMARY KEY (`id`)" +
            ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='功放数据表_%s'",
            tableName, tableName
        );

        return executeCreateTable(tableName, createTableSql);
    }

    /**
     * 根据设备类型创建对应的表
     *
     * @param sn 设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @return true-创建成功，false-创建失败
     */
    public boolean createDeviceTable(String sn, String deviceCode, String deviceType) {
        String tableName = generateTableName(sn, deviceCode, deviceType);

        switch (deviceType.toLowerCase()) {
            case "gps":
                return createGpsTable(tableName);
            case "aws":
                return createAwsTable(tableName);
            case "modem":
                return createModemTable(tableName);
            case "pdu":
                return createPduTable(tableName);
            case "attitude":
                return createAttitudeTable(tableName);
            case "amplifier":
                return createAmplifierTable(tableName);
            default:
                logger.warn("不支持的设备类型: {}", deviceType);
                return false;
        }
    }

    /**
     * 执行创建表的SQL语句
     * 
     * @param tableName 表名
     * @param createTableSql 创建表的SQL语句
     * @return true-创建成功，false-创建失败
     */
    private boolean executeCreateTable(String tableName, String createTableSql) {
        try {
            jdbcTemplate.execute(createTableSql);
            logger.info("成功创建表: {}", tableName);
            return true;
        } catch (DataAccessException e) {
            logger.error("创建表 {} 失败: {}", tableName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取所有设备数据表列表
     * 
     * @param deviceType 设备类型（可选）
     * @return 表名列表
     */
    public List<String> getDeviceTableList(String deviceType) {
        String sql;
        if (deviceType != null && !deviceType.trim().isEmpty()) {
            sql = "SELECT table_name FROM information_schema.tables " +
                  "WHERE table_schema = DATABASE() AND table_name LIKE ? " +
                  "ORDER BY table_name";
            return jdbcTemplate.queryForList(sql, String.class, "%_" + deviceType.toLowerCase() + "_%");
        } else {
            sql = "SELECT table_name FROM information_schema.tables " +
                  "WHERE table_schema = DATABASE() AND table_name REGEXP '^[^_]+_(gps|aws|modem|pdu|attitude|amplifier)_[^_]+$' " +
                  "ORDER BY table_name";
            return jdbcTemplate.queryForList(sql, String.class);
        }
    }

    /**
     * 删除设备数据表
     * 
     * @param tableName 表名
     * @return true-删除成功，false-删除失败
     */
    public boolean dropDeviceTable(String tableName) {
        if (!tableExists(tableName)) {
            logger.info("表 {} 不存在，无需删除", tableName);
            return true;
        }

        try {
            String dropTableSql = String.format("DROP TABLE `%s`", tableName);
            jdbcTemplate.execute(dropTableSql);
            logger.info("成功删除表: {}", tableName);
            return true;
        } catch (DataAccessException e) {
            logger.error("删除表 {} 失败: {}", tableName, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取表的基本信息
     * 
     * @param tableName 表名
     * @return 表信息
     */
    public Map<String, Object> getTableInfo(String tableName) {
        try {
            String sql = "SELECT table_name, table_comment, create_time, update_time, table_rows, data_length " +
                        "FROM information_schema.tables " +
                        "WHERE table_schema = DATABASE() AND table_name = ?";
            List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, tableName);
            return result.isEmpty() ? null : result.get(0);
        } catch (DataAccessException e) {
            logger.error("获取表信息失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
