package com.snct.system.service;

import com.snct.dctcore.commoncore.domain.transfer.TransferPackage;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.commoncore.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: DeviceDataMysqlService
 * @Description: 设备数据MySQL服务
 * @author: wzewei
 * @date: 2025-09-03 17:53:27
 */
@Service
public class DeviceDataMysqlService {

    private static final Logger logger = LoggerFactory.getLogger(DeviceDataMysqlService.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DynamicTableService dynamicTableService;

    /**
     * 保存设备数据到MySQL
     *
     * @param transferPackage 传输包
     * @return true-保存成功，false-保存失败
     */
    public boolean saveDeviceData(TransferPackage transferPackage) {
        try {
            // 获取设备类型
            DeviceTypeEnum deviceTypeEnum = DeviceTypeEnum.getByValue(transferPackage.getDeviceType());
            if (deviceTypeEnum == null) {
                logger.warn("不支持的设备类型: {}", transferPackage.getDeviceType());
                return false;
            }

            String deviceType = deviceTypeEnum.getAlias().toLowerCase();
            String tableName = dynamicTableService.generateTableName(
                    transferPackage.getSn(),
                    transferPackage.getDeviceCode(),
                    deviceType
            );

            // 检查并创建表
            if (!dynamicTableService.tableExists(tableName)) {
                boolean created = dynamicTableService.createDeviceTable(
                        transferPackage.getSn(),
                        transferPackage.getDeviceCode(),
                        deviceType
                );
                if (!created) {
                    logger.error("创建表失败: {}", tableName);
                    return false;
                }
            }

            // 根据设备类型保存数据
            switch (deviceType) {
                case "gps":
                    return saveGpsData(tableName, transferPackage);
                case "aws":
                    return saveAwsData(tableName, transferPackage);
                case "modem":
                    return saveModemData(tableName, transferPackage);
                case "pdu":
                    return savePduData(tableName, transferPackage);
                case "attitude":
                    return saveAttitudeData(tableName, transferPackage);
                case "amplifier":
                    return saveAmplifierData(tableName, transferPackage);
                default:
                    logger.warn("不支持的设备类型: {}", deviceType);
                    return false;
            }
        } catch (Exception e) {
            logger.error("保存设备数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存GPS数据
     *
     * @param tableName       表名
     * @param transferPackage 传输包
     * @return true-保存成功，false-保存失败
     */
    private boolean saveGpsData(String tableName, TransferPackage transferPackage) {
        try {
            // 解析GPS数据
            Map<String, Object> gpsData = parseGpsMessage(transferPackage.getMessage());

            String sql = String.format(
                    "INSERT INTO `%s` (initial_time, initial_bj_time, `utc_time`, latitude_hemisphere, " +
                            "longitude_hemisphere, latitude, longitude) VALUES (?, ?, ?, ?, ?, ?, ?)",
                    tableName
            );

            Date bjTime = new Date(transferPackage.getTime());

            int result = jdbcTemplate.update(sql,
                    transferPackage.getTime(),
                    bjTime,
                    gpsData.get("utcTime"),
                    gpsData.get("latitudeHemisphere"),
                    gpsData.get("longitudeHemisphere"),
                    gpsData.get("latitude"),
                    gpsData.get("longitude")
            );
            return result > 0;
        } catch (DataAccessException e) {
            logger.error("保存GPS数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存AWS气象数据
     *
     * @param tableName       表名
     * @param transferPackage 传输包
     * @return true-保存成功，false-保存失败
     */
    private boolean saveAwsData(String tableName, TransferPackage transferPackage) {
        try {
            // 解析AWS数据
            Map<String, Object> awsData = parseAwsMessage(transferPackage.getMessage());

            String sql = String.format(
                    "INSERT INTO `%s` (initial_time, initial_bj_time,`utc_time`, relative_wind, relative_wind_speed, " +
                            "air_temperature, humidity, point_tem, pressure, qfe, qnh, dp) VALUES (?, ?, ?, ?, ?, ?, " +
                            "?, ?, ?, ?, ?, ?)",
                    tableName
            );

            Date bjTime = new Date(transferPackage.getTime());

            int result = jdbcTemplate.update(sql,
                    transferPackage.getTime(),
                    bjTime,
                    awsData.get("utcTime"),
                    awsData.get("relativeWind"),
                    awsData.get("relativeWindSpeed"),
                    awsData.get("airTemperature"),
                    awsData.get("humidity"),
                    awsData.get("pointTem"),
                    awsData.get("pressure"),
                    awsData.get("qfe"),
                    awsData.get("qnh"),
                    awsData.get("dp")
            );
            return result > 0;
        } catch (DataAccessException e) {
            logger.error("保存AWS数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存卫星猫数据
     *
     * @param tableName       表名
     * @param transferPackage 传输包
     * @return true-保存成功，false-保存失败
     */
    private boolean saveModemData(String tableName, TransferPackage transferPackage) {
        try {
            // 解析卫星猫数据
            Map<String, Object> modemData = parseModemMessage(transferPackage.getMessage());

            String sql = String.format(
                    "INSERT INTO `%s` (initial_time, initial_bj_time, `utc_time`, `signal`, speed, send_power, flag) " +
                            "VALUES (?, ?, ?, ?, ?, ?, ?)",
                    tableName
            );

            Date bjTime = new Date(transferPackage.getTime());

            int result = jdbcTemplate.update(sql,
                    transferPackage.getTime(),
                    bjTime,
                    modemData.get("utcTime"),
                    modemData.get("signal"),
                    modemData.get("speed"),
                    modemData.get("sendPower"),
                    modemData.get("flag")
            );
            return result > 0;
        } catch (DataAccessException e) {
            logger.error("保存卫星猫数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存PDU数据
     *
     * @param tableName       表名
     * @param transferPackage 传输包
     * @return true-保存成功，false-保存失败
     */
    private boolean savePduData(String tableName, TransferPackage transferPackage) {
        try {
            // 解析PDU数据
            Map<String, Object> pduData = parsePduMessage(transferPackage.getMessage());

            String sql = String.format(
                    "INSERT INTO `%s` (initial_time, initial_bj_time, `utc_time`, manage, electric, voltage, " +
                            "yes_power, no_power, " +
                            "see_power, power_param, out1_electric, out1_power, out1_status, out2_electric, " +
                            "out2_power, out2_status, " +
                            "out3_electric, out3_power, out3_status, out4_electric, out4_power, out4_status, " +
                            "out5_electric, out5_power, " +
                            "out5_status, out6_electric, out6_power, out6_status, out7_electric, out7_power, " +
                            "out7_status, out8_electric, " +
                            "out8_power, out8_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, " +
                            "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
                    tableName
            );

            Date bjTime = new Date(transferPackage.getTime());

            int result = jdbcTemplate.update(sql,
                    transferPackage.getTime(),
                    bjTime,
                    pduData.get("utcTime"),
                    pduData.get("manage"),
                    pduData.get("electric"),
                    pduData.get("voltage"),
                    pduData.get("yesPower"),
                    pduData.get("noPower"),
                    pduData.get("seePower"),
                    pduData.get("powerParam"),
                    pduData.get("out1Electric"),
                    pduData.get("out1Power"),
                    pduData.get("out1Status"),
                    pduData.get("out2Electric"),
                    pduData.get("out2Power"),
                    pduData.get("out2Status"),
                    pduData.get("out3Electric"),
                    pduData.get("out3Power"),
                    pduData.get("out3Status"),
                    pduData.get("out4Electric"),
                    pduData.get("out4Power"),
                    pduData.get("out4Status"),
                    pduData.get("out5Electric"),
                    pduData.get("out5Power"),
                    pduData.get("out5Status"),
                    pduData.get("out6Electric"),
                    pduData.get("out6Power"),
                    pduData.get("out6Status"),
                    pduData.get("out7Electric"),
                    pduData.get("out7Power"),
                    pduData.get("out7Status"),
                    pduData.get("out8Electric"),
                    pduData.get("out8Power"),
                    pduData.get("out8Status")
            );
            return result > 0;
        } catch (DataAccessException e) {
            logger.error("保存PDU数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存姿态仪数据
     *
     * @param tableName       表名
     * @param transferPackage 传输包
     * @return true-保存成功，false-保存失败
     */
    private boolean saveAttitudeData(String tableName, TransferPackage transferPackage) {
        try {
            // 解析姿态仪数据
            Map<String, Object> attitudeData = parseAttitudeMessage(transferPackage.getMessage());

            String sql = String.format(
                    "INSERT INTO `%s` (initial_time, initial_bj_time, `utc_time`, lat, lon, rolling, " +
                            "pitch, height, heading, speed, distance, station_name) VALUES (?, ?, ?, ?, ?, ?, ?, ?, " +
                            "?, ?, ?, ?)",
                    tableName
            );

            Date bjTime = new Date(transferPackage.getTime());

            int result = jdbcTemplate.update(sql,
                    transferPackage.getTime(),
                    bjTime,
                    attitudeData.get("utcTime"),
                    attitudeData.get("lat"),
                    attitudeData.get("lon"),
                    attitudeData.get("rolling"),
                    attitudeData.get("pitch"),
                    attitudeData.get("height"),
                    attitudeData.get("heading"),
                    attitudeData.get("speed"),
                    attitudeData.get("distance"),
                    attitudeData.get("stationName")
            );
            return result > 0;
        } catch (DataAccessException e) {
            logger.error("保存姿态仪数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 保存功放数据
     *
     * @param tableName       表名
     * @param transferPackage 传输包
     * @return true-保存成功，false-保存失败
     */
    private boolean saveAmplifierData(String tableName, TransferPackage transferPackage) {
        try {
            // 解析功放数据
            Map<String, Object> amplifierData = parseAmplifierMessage(transferPackage.getMessage());

            String sql = String.format(
                    "INSERT INTO `%s` (initial_time, initial_bj_time, `utc_time`, decay, temp, out_power, status) " +
                            "VALUES (?, ?, ?, ?, ?, ?, ?)",
                    tableName
            );

            Date bjTime = new Date(transferPackage.getTime());

            int result = jdbcTemplate.update(sql,
                    transferPackage.getTime(),
                    bjTime,
                    amplifierData.get("utcTime"),
                    amplifierData.get("decay"),
                    amplifierData.get("temp"),
                    amplifierData.get("outPower"),
                    amplifierData.get("status")
            );
            return result > 0;
        } catch (DataAccessException e) {
            logger.error("保存功放数据失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析GPS消息
     *
     * @param message 消息内容（已拼接好的GPS数据字符串）
     * @return 解析后的数据
     */
    private Map<String, Object> parseGpsMessage(String message) {
        Map<String, Object> data = new HashMap<>();

        try {
            // message格式：utcTime,latitudeRes,latitudeHemisphere,longitudeRes,longitudeHemisphere
            String[] parts = message.split(",");
            if (parts.length >= 5) {
                data.put("utcTime", parts[0]);
                data.put("latitude", parts[1]);
                data.put("latitudeHemisphere", parts[2]);
                data.put("longitude", parts[3]);
                data.put("longitudeHemisphere", parts[4]);
            }
        } catch (Exception e) {
            logger.warn("GPS消息解析失败: {}", e.getMessage());
        }

        return data;
    }


    /**
     * 解析AWS消息
     *
     * @param message 消息内容（已拼接好的AWS数据字符串）
     * @return 解析后的数据
     */
    private Map<String, Object> parseAwsMessage(String message) {
        Map<String, Object> data = new HashMap<>();

        try {
            // message格式：utcTime,relativeWindSpeed,relativeWind,airTemperature,humidity,pointTem,pressure,qfe,qnh,dp
            String[] parts = message.split(",", -1); // 使用-1保留空字符串
            if (parts.length >= 9) {
                data.put("utcTime", parts[0]);
                data.put("relativeWindSpeed", parts[1]);
                data.put("relativeWind", parts[2]);
                data.put("airTemperature", parts[3]);
                data.put("humidity", parts[4]);
                data.put("pointTem", parts[5]);
                data.put("pressure", parts[6]);
                data.put("qfe", parts[7]);
                data.put("qnh", parts[8]);
                data.put("dp", parts.length > 9 ? parts[9] : ""); // 如果没有第10个字段，设为空字符串
            } else {
                logger.warn("AWS数据格式不正确，期望至少9个字段，实际: {}, 数据: {}", parts.length, message);
            }
        } catch (Exception e) {
            logger.warn("AWS消息解析失败: {}", e.getMessage());
        }

        return data;
    }

    /**
     * 解析卫星猫消息
     *
     * @param message 消息内容（已拼接好的Modem数据字符串）
     * @return 解析后的数据
     */
    private Map<String, Object> parseModemMessage(String message) {
        Map<String, Object> data = new HashMap<>();

        try {
            // message格式：utcTime,signal,speed,sendPower,flag
            String[] parts = message.split(",", -1);
            if (parts.length >= 5) {
                data.put("utcTime", parts[0]);
                data.put("signal", parts[1]);
                data.put("speed", parts[2]);
                data.put("sendPower", parts[3]);
                data.put("flag", parts[4]);
            } else {
                logger.warn("Modem数据格式不正确，期望至少5个字段，实际: {}, 数据: {}", parts.length, message);
            }
        } catch (Exception e) {
            logger.warn("卫星猫消息解析失败: {}", e.getMessage());
        }

        return data;
    }

    /**
     * 解析PDU消息
     *
     * @param message 消息内容（已拼接好的PDU数据字符串）
     * @return 解析后的数据
     */
    private Map<String, Object> parsePduMessage(String message) {
        Map<String, Object> data = new HashMap<>();

        try {
            // message格式：utcTime,electric,manage,voltage,yesPower,noPower,seePower,powerParam,1,electric1,power1,
            // status1,2,electric2,power2,status2,...
            String[] parts = message.split(",", -1);
            if (parts.length >= 40) {
                data.put("utcTime", parts[0]);
                data.put("electric", parts[1]);
                data.put("manage", parts[2]);
                data.put("voltage", parts[3]);
                data.put("yesPower", parts[4]);
                data.put("noPower", parts[5]);
                data.put("seePower", parts[6]);
                data.put("powerParam", parts[7]);
                // 跳过插座编号，只取电流、功率、状态
                data.put("out1Electric", parts[9]);
                data.put("out1Power", parts[10]);
                data.put("out1Status", parts[11]);
                data.put("out2Electric", parts[13]);
                data.put("out2Power", parts[14]);
                data.put("out2Status", parts[15]);
                data.put("out3Electric", parts[17]);
                data.put("out3Power", parts[18]);
                data.put("out3Status", parts[19]);
                data.put("out4Electric", parts[21]);
                data.put("out4Power", parts[22]);
                data.put("out4Status", parts[23]);
                data.put("out5Electric", parts[25]);
                data.put("out5Power", parts[26]);
                data.put("out5Status", parts[27]);
                data.put("out6Electric", parts[29]);
                data.put("out6Power", parts[30]);
                data.put("out6Status", parts[31]);
                data.put("out7Electric", parts[33]);
                data.put("out7Power", parts[34]);
                data.put("out7Status", parts[35]);
                data.put("out8Electric", parts[37]);
                data.put("out8Power", parts[38]);
                data.put("out8Status", parts[39]);
            } else {
                logger.warn("PDU数据格式不正确，期望至少40个字段，实际: {}, 数据: {}", parts.length, message);
            }
        } catch (Exception e) {
            logger.warn("PDU消息解析失败: {}", e.getMessage());
        }

        return data;
    }

    /**
     * 解析姿态仪消息
     *
     * @param message 消息内容（已拼接好的Attitude数据字符串）
     * @return 解析后的数据
     */
    private Map<String, Object> parseAttitudeMessage(String message) {
        Map<String, Object> data = new HashMap<>();

        try {
            // message格式：utcTime,rolling,pitch,height,heading,stationName,lon,lat
            String[] parts = message.split(",");
            if (parts.length >= 8) {
                data.put("utcTime", parts[0]);
                data.put("rolling", parts[1]);
                data.put("pitch", parts[2]);
                data.put("height", parts[3]);
                data.put("heading", parts[4]);
                data.put("stationName", parts[5]);
                data.put("lon", parts[6]);
                data.put("lat", parts[7]);
                data.put("speed", ""); // 原始数据中没有speed字段
                data.put("distance", ""); // 原始数据中没有distance字段
            }
        } catch (Exception e) {
            logger.warn("姿态仪消息解析失败: {}", e.getMessage());
        }

        return data;
    }

    /**
     * 解析功放消息
     *
     * @param message 消息内容（已拼接好的Amplifier数据字符串）
     * @return 解析后的数据
     */
    private Map<String, Object> parseAmplifierMessage(String message) {
        Map<String, Object> data = new HashMap<>();

        try {
            // message格式：utcTime,decay,temp,outPower,status
            String[] parts = message.split(",", -1);
            if (parts.length >= 5) {
                data.put("utcTime", parts[0]);
                data.put("decay", parts[1]);
                data.put("temp", parts[2]);
                data.put("outPower", parts[3]);
                data.put("status", parts[4]);
            } else {
                logger.warn("功放数据格式不正确，期望至少5个字段，实际: {}, 数据: {}", parts.length, message);
            }
        } catch (Exception e) {
            logger.warn("功放消息解析失败: {}", e.getMessage());
        }

        return data;
    }

    /**
     * 查询设备数据
     *
     * @param sn         设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @param startTime  开始时间
     * @param endTime    结束时间
     * @param limit      限制条数
     * @return 数据列表
     */
    public List<Map<String, Object>> queryDeviceData(String sn, String deviceCode, String deviceType,
                                                     Long startTime, Long endTime, Integer limit) {
        try {
            String tableName = dynamicTableService.generateTableName(sn, deviceCode, deviceType);

            if (!dynamicTableService.tableExists(tableName)) {
                logger.warn("表不存在: {}", tableName);
                return new ArrayList<>();
            }

            StringBuilder sql = new StringBuilder(String.format("SELECT * FROM `%s` WHERE 1=1", tableName));

            if (startTime != null) {
                sql.append(" AND initial_time >= ").append(startTime);
            }
            if (endTime != null) {
                sql.append(" AND initial_time <= ").append(endTime);
            }

            sql.append(" ORDER BY initial_time DESC");

            if (limit != null && limit > 0) {
                sql.append(" LIMIT ").append(limit);
            }

            return jdbcTemplate.queryForList(sql.toString());
        } catch (DataAccessException e) {
            logger.error("查询设备数据失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取设备最新数据
     *
     * @param sn         设备序列号
     * @param deviceCode 设备编号
     * @param deviceType 设备类型
     * @return 最新数据
     */
    public Map<String, Object> getLatestDeviceData(String sn, String deviceCode, String deviceType) {
        List<Map<String, Object>> result = queryDeviceData(sn, deviceCode, deviceType, null, null, 1);
        return result.isEmpty() ? null : result.get(0);
    }
}
