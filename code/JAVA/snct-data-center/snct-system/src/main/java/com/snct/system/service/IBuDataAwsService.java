package com.snct.system.service;

import java.util.List;
import com.snct.system.domain.data.BuDataAws;

/**
 * AWS气象数据Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-03
 */
public interface IBuDataAwsService 
{
    /**
     * 查询AWS气象数据
     * 
     * @param id AWS气象数据主键
     * @return AWS气象数据
     */
    public BuDataAws selectBuDataAwsById(Long id);

    /**
     * 查询AWS气象数据列表
     * 
     * @param buDataAws AWS气象数据
     * @return AWS气象数据集合
     */
    public List<BuDataAws> selectBuDataAwsList(BuDataAws buDataAws);

    /**
     * 新增AWS气象数据
     * 
     * @param buDataAws AWS气象数据
     * @return 结果
     */
    public int insertBuDataAws(BuDataAws buDataAws);

    /**
     * 修改AWS气象数据
     * 
     * @param buDataAws AWS气象数据
     * @return 结果
     */
    public int updateBuDataAws(BuDataAws buDataAws);

    /**
     * 批量删除AWS气象数据
     * 
     * @param ids 需要删除的AWS气象数据主键集合
     * @return 结果
     */
    public int deleteBuDataAwsByIds(Long[] ids);

    /**
     * 删除AWS气象数据信息
     * 
     * @param id AWS气象数据主键
     * @return 结果
     */
    public int deleteBuDataAwsById(Long id);
}
